{% extends "base.html" %}

{% block title %}Search Documents - KAG Document Processing{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-search me-2"></i>Search Documents
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-brain me-2"></i>Natural Language Search
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="searchForm">
                    <div class="mb-3">
                        <label for="query" class="form-label">Search Query</label>
                        <textarea class="form-control" id="query" name="query" rows="3" 
                                placeholder="Enter your question or search query here..." required></textarea>
                        <div class="form-text">
                            Ask questions in natural language. For example: "What are the main topics discussed?" or "Find information about machine learning"
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="top_k" class="form-label">Number of Results</label>
                            <select class="form-select" id="top_k" name="top_k">
                                <option value="3">3 results</option>
                                <option value="5" selected>5 results</option>
                                <option value="10">10 results</option>
                                <option value="15">15 results</option>
                                <option value="20">20 results</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Options</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="debug" name="debug">
                                <label class="form-check-label" for="debug">
                                    Enable debug mode (show detailed search information)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_flow" name="show_flow">
                                <label class="form-check-label" for="show_flow">
                                    Show search flow (step-by-step process visualization)
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg" id="searchBtn">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Search Examples -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Example Queries
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>General Questions:</h6>
                        <ul class="list-unstyled">
                            <li><a href="#" class="example-query text-decoration-none">What are the main topics discussed in the documents?</a></li>
                            <li><a href="#" class="example-query text-decoration-none">Summarize the key findings</a></li>
                            <li><a href="#" class="example-query text-decoration-none">What are the most important concepts?</a></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Specific Searches:</h6>
                        <ul class="list-unstyled">
                            <li><a href="#" class="example-query text-decoration-none">Find information about machine learning</a></li>
                            <li><a href="#" class="example-query text-decoration-none">What does the document say about data analysis?</a></li>
                            <li><a href="#" class="example-query text-decoration-none">Show me details about methodology</a></li>
                        </ul>

                        <h6 class="mt-3">Chinese Queries:</h6>
                        <ul class="list-unstyled">
                            <li><a href="#" class="example-query text-decoration-none">列出所有庫易股票資料</a></li>
                            <li><a href="#" class="example-query text-decoration-none">什麼是機器學習</a></li>
                            <li><a href="#" class="example-query text-decoration-none">顯示財務數據分析</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Loading Modal -->
<div class="modal fade" id="searchModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-search me-2"></i>Searching Documents
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mb-0">Analyzing documents and generating response...</p>
                <small class="text-muted">This may take a few moments</small>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Example query click handler
    $('.example-query').click(function(e) {
        e.preventDefault();
        $('#query').val($(this).text());
        $('#query').focus();
    });
    
    // Form submission handler
    $('#searchForm').submit(function(e) {
        const query = $('#query').val().trim();
        if (!query) {
            e.preventDefault();
            alert('Please enter a search query.');
            return;
        }
        
        // Show search modal
        $('#searchModal').modal('show');
        
        // Disable search button
        $('#searchBtn').prop('disabled', true).html(
            '<i class="fas fa-spinner fa-spin me-2"></i>Searching...'
        );
    });
    
    // Auto-resize textarea
    $('#query').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Keyboard shortcuts
    $(document).keydown(function(e) {
        // Ctrl+Enter to submit search
        if (e.ctrlKey && e.keyCode === 13) {
            $('#searchForm').submit();
        }
        // Escape to clear search
        if (e.keyCode === 27) {
            $('#query').val('').focus();
        }
    });
    
    // Focus on search input when page loads
    $('#query').focus();
});
</script>
{% endblock %}
