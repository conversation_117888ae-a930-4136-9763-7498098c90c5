{% extends "base.html" %}

{% block title %}Document Details - KAG Document Processing{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-file-alt me-2"></i>Document Details
            </h1>
            <div>
                <a href="{{ url_for('processing_status', doc_id=doc_id) }}" class="btn btn-outline-info">
                    <i class="fas fa-info-circle me-2"></i>Processing Status
                </a>
                <a href="{{ url_for('documents_page') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Documents
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Document Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Document Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Document ID:</strong> {{ doc_id }}</p>
                        {% if status %}
                        <p><strong>File Name:</strong> {{ status.fileName or 'N/A' }}</p>
                        <p><strong>Current Status:</strong> 
                            {% if status.currentStep == 'completed' %}
                                <span class="badge bg-success">Completed</span>
                            {% elif status.currentStep == 'failed' %}
                                <span class="badge bg-danger">Failed</span>
                            {% elif status.currentStep %}
                                <span class="badge bg-warning">{{ status.currentStep.title() }}</span>
                            {% else %}
                                <span class="badge bg-secondary">Unknown</span>
                            {% endif %}
                        </p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        <p><strong>Total Chunks:</strong> {{ chunks|length }}</p>
                        {% if status and status.errorMessage %}
                        <p><strong>Error Message:</strong> 
                            <span class="text-danger">{{ status.errorMessage }}</span>
                        </p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Document Chunks -->
{% if chunks %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">
                            <i class="fas fa-puzzle-piece me-2"></i>Document Chunks ({{ chunks|length }})
                        </h5>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="chunkSearch" 
                                   placeholder="Search within chunks...">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chunks-container" style="max-height: 600px; overflow-y: auto;">
                    {% for chunk in chunks %}
                    <div class="chunk-item mb-3 p-3 border rounded" data-chunk-index="{{ loop.index }}">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="text-primary mb-0">
                                <i class="fas fa-file-text me-2"></i>Chunk {{ loop.index }}
                            </h6>
                            <small class="text-muted">
                                {{ chunk.content|length }} characters
                            </small>
                        </div>
                        <div class="chunk-content">
                            <p class="mb-0">{{ chunk.content }}</p>
                        </div>
                        <div class="chunk-actions mt-2">
                            <button class="btn btn-outline-primary btn-sm copy-chunk" 
                                    data-content="{{ chunk.content|e }}">
                                <i class="fas fa-copy me-1"></i>Copy
                            </button>
                            <button class="btn btn-outline-info btn-sm expand-chunk">
                                <i class="fas fa-expand me-1"></i>Expand
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-puzzle-piece fa-4x text-muted mb-4"></i>
                <h4>No Chunks Available</h4>
                <p class="text-muted mb-4">
                    {% if status and status.currentStep == 'failed' %}
                        Document processing failed. Please check the processing status for more details.
                    {% elif status and status.currentStep != 'completed' %}
                        Document is still being processed. Chunks will appear once processing is complete.
                    {% else %}
                        No chunks found for this document.
                    {% endif %}
                </p>
                <a href="{{ url_for('processing_status', doc_id=doc_id) }}" class="btn btn-primary">
                    <i class="fas fa-info-circle me-2"></i>Check Processing Status
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Chunk Expansion Modal -->
<div class="modal fade" id="chunkModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-text me-2"></i>Chunk Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label"><strong>Chunk Content:</strong></label>
                    <div class="form-control" style="height: 300px; overflow-y: auto;" id="modalChunkContent">
                        <!-- Content will be inserted here -->
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>Character Count:</strong></label>
                    <span id="modalCharCount" class="badge bg-info"></span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-primary" id="modalCopyBtn">
                    <i class="fas fa-copy me-1"></i>Copy Content
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.chunk-item {
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.chunk-item:hover {
    background-color: #e9ecef;
    border-color: #007bff !important;
}

.chunk-content {
    max-height: 150px;
    overflow-y: auto;
    font-size: 0.9rem;
    line-height: 1.5;
}

.chunk-actions {
    border-top: 1px solid #dee2e6;
    padding-top: 0.5rem;
}

.chunks-container {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}

.highlight {
    background-color: yellow;
    padding: 0.1rem 0.2rem;
    border-radius: 0.2rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Search within chunks
    $('#chunkSearch').on('keyup', function() {
        const searchTerm = $(this).val().toLowerCase();
        
        $('.chunk-item').each(function() {
            const chunkText = $(this).find('.chunk-content').text().toLowerCase();
            const isVisible = chunkText.includes(searchTerm);
            $(this).toggle(isVisible);
            
            // Highlight search terms
            if (searchTerm && isVisible) {
                highlightText($(this).find('.chunk-content p'), searchTerm);
            } else {
                removeHighlight($(this).find('.chunk-content p'));
            }
        });
    });
    
    // Copy chunk content
    $('.copy-chunk').click(function() {
        const content = $(this).data('content');
        navigator.clipboard.writeText(content).then(function() {
            // Show success feedback
            const btn = $(this);
            const originalText = btn.html();
            btn.html('<i class="fas fa-check me-1"></i>Copied!');
            setTimeout(function() {
                btn.html(originalText);
            }, 2000);
        }.bind(this));
    });
    
    // Expand chunk in modal
    $('.expand-chunk').click(function() {
        const chunkItem = $(this).closest('.chunk-item');
        const chunkIndex = chunkItem.data('chunk-index');
        const content = chunkItem.find('.chunk-content p').text();
        
        $('#modalChunkContent').text(content);
        $('#modalCharCount').text(content.length + ' characters');
        $('.modal-title').html(`<i class="fas fa-file-text me-2"></i>Chunk ${chunkIndex} Details`);
        $('#chunkModal').modal('show');
    });
    
    // Copy from modal
    $('#modalCopyBtn').click(function() {
        const content = $('#modalChunkContent').text();
        navigator.clipboard.writeText(content).then(function() {
            const btn = $('#modalCopyBtn');
            const originalText = btn.html();
            btn.html('<i class="fas fa-check me-1"></i>Copied!');
            setTimeout(function() {
                btn.html(originalText);
            }, 2000);
        });
    });
    
    // Highlight text function
    function highlightText(element, searchTerm) {
        const text = element.text();
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        const highlightedText = text.replace(regex, '<span class="highlight">$1</span>');
        element.html(highlightedText);
    }
    
    // Remove highlight function
    function removeHighlight(element) {
        const text = element.text();
        element.text(text);
    }
    
    // Auto-scroll to first visible chunk when searching
    $('#chunkSearch').on('keyup', function() {
        setTimeout(function() {
            const firstVisible = $('.chunk-item:visible').first();
            if (firstVisible.length) {
                firstVisible[0].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }
        }, 100);
    });
});
</script>
{% endblock %}
