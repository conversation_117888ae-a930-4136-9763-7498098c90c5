{% extends "base.html" %}

{% block title %}Upload Document - KAG Document Processing{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-upload me-2"></i>Upload Document
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-upload me-2"></i>Select File to Process
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <div class="mb-4">
                        <label for="file" class="form-label">Choose File</label>
                        <input type="file" class="form-control" id="file" name="file" 
                               accept=".pdf,.mp4,.avi,.mov,.mkv,.wmv" required>
                        <div class="form-text">
                            Supported formats: PDF, MP4, AVI, MOV, MKV, WMV (Max size: 50MB)
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Processing Information:</strong>
                            <ul class="mb-0 mt-2">
                                <li>PDF files will be processed for text extraction and knowledge graph generation</li>
                                <li>Video files will be processed for audio transcription and content analysis</li>
                                <li>Processing may take several minutes depending on file size and complexity</li>
                                <li>You'll be redirected to a status page to monitor progress</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg" id="uploadBtn">
                            <i class="fas fa-upload me-2"></i>Upload and Process
                        </button>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Upload Progress Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>Uploading File
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mb-0">Please wait while your file is being uploaded...</p>
                <small class="text-muted">Do not close this window</small>
            </div>
        </div>
    </div>
</div>

<!-- File Preview Area -->
<div class="row mt-4" id="filePreview" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-eye me-2"></i>File Preview
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>File Name:</strong> <span id="fileName"></span>
                    </div>
                    <div class="col-md-6">
                        <strong>File Size:</strong> <span id="fileSize"></span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <strong>File Type:</strong> <span id="fileType"></span>
                    </div>
                    <div class="col-md-6">
                        <strong>Last Modified:</strong> <span id="lastModified"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // File input change handler
    $('#file').change(function() {
        const file = this.files[0];
        if (file) {
            // Show file preview
            $('#fileName').text(file.name);
            $('#fileSize').text(formatFileSize(file.size));
            $('#fileType').text(file.type || 'Unknown');
            $('#lastModified').text(new Date(file.lastModified).toLocaleString());
            $('#filePreview').show();
            
            // Validate file size
            if (file.size > 50 * 1024 * 1024) { // 50MB
                alert('File size exceeds 50MB limit. Please choose a smaller file.');
                $(this).val('');
                $('#filePreview').hide();
                return;
            }
        } else {
            $('#filePreview').hide();
        }
    });
    
    // Form submission handler
    $('#uploadForm').submit(function(e) {
        const file = $('#file')[0].files[0];
        if (!file) {
            e.preventDefault();
            alert('Please select a file to upload.');
            return;
        }
        
        // Show upload modal
        $('#uploadModal').modal('show');
        
        // Disable upload button
        $('#uploadBtn').prop('disabled', true).html(
            '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...'
        );
    });
    
    // Format file size helper function
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // Drag and drop functionality
    const dropZone = $('.card-body');
    
    dropZone.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('border-primary bg-light');
    });
    
    dropZone.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('border-primary bg-light');
    });
    
    dropZone.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('border-primary bg-light');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            $('#file')[0].files = files;
            $('#file').trigger('change');
        }
    });
});
</script>
{% endblock %}
