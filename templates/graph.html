{% extends "base.html" %}

{% block title %}Knowledge Graph - KAG Document Processing{% endblock %}

{% block extra_css %}
<style>
#graph-container {
    width: 100%;
    height: 600px;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
}

.graph-controls {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.legend-item {
    display: inline-block;
    margin-right: 1rem;
    margin-bottom: 0.8rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 4px 8px;
    background-color: #ffffff;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
    vertical-align: middle;
    border: 1px solid rgba(0,0,0,0.1);
}

.graph-stats {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
}

#loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-project-diagram me-2"></i>Knowledge Graph Visualization
        </h1>
    </div>
</div>

<!-- Graph Controls -->
<div class="row mb-4">
    <div class="col-12">
        <div class="graph-controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="mb-2">Graph Controls</h6>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary" id="zoomIn">
                            <i class="fas fa-search-plus"></i> Zoom In
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="zoomOut">
                            <i class="fas fa-search-minus"></i> Zoom Out
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="resetView">
                            <i class="fas fa-home"></i> Reset View
                        </button>
                        <button type="button" class="btn btn-outline-success" id="refreshGraph">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6 class="mb-2">Display Options</h6>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="showLabels" checked>
                        <label class="form-check-label" for="showLabels">Show Labels</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="showLinks" checked>
                        <label class="form-check-label" for="showLinks">Show Links</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graph Visualization -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-network-wired me-2"></i>Interactive Knowledge Graph
                </h5>
            </div>
            <div class="card-body p-0">
                <div id="graph-container" class="position-relative">
                    <div id="loading-spinner" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading graph...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading knowledge graph...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graph Statistics and Legend -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Graph Statistics
                </h6>
            </div>
            <div class="card-body">
                <div class="graph-stats">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary mb-1" id="nodeCount">-</h4>
                            <small class="text-muted">Entities</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success mb-1" id="linkCount">-</h4>
                            <small class="text-muted">Relationships</small>
                        </div>
                    </div>
                </div>
                <hr>
                <div id="categoryStats">
                    <!-- Category statistics will be populated here -->
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-palette me-2"></i>Legend
                </h6>
            </div>
            <div class="card-body">
                <div id="legend">
                    <!-- Legend will be populated dynamically -->
                </div>
                <hr>
                <div class="mt-3">
                    <h6>Interaction Guide:</h6>
                    <ul class="list-unstyled small">
                        <li><i class="fas fa-mouse-pointer me-2"></i>Click and drag to move nodes</li>
                        <li><i class="fas fa-search me-2"></i>Scroll to zoom in/out</li>
                        <li><i class="fas fa-hand-paper me-2"></i>Click on nodes to see details</li>
                        <li><i class="fas fa-arrows-alt me-2"></i>Drag background to pan</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Node Details Modal -->
<div class="modal fade" id="nodeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>Entity Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label"><strong>Name:</strong></label>
                    <p id="modalNodeName" class="form-control-plaintext"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>Category:</strong></label>
                    <p id="modalNodeCategory" class="form-control-plaintext"></p>
                </div>
                <div class="mb-3">
                    <label class="form-label"><strong>Connections:</strong></label>
                    <p id="modalNodeConnections" class="form-control-plaintext"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<!-- Include Force Graph library -->
<script src="https://unpkg.com/force-graph@1.43.0/dist/force-graph.min.js"></script>

<script>
let graph;
let graphData = null;

$(document).ready(function() {
    loadGraphData();
    
    // Control button handlers
    $('#zoomIn').click(() => graph && graph.zoom(graph.zoom() * 1.5));
    $('#zoomOut').click(() => graph && graph.zoom(graph.zoom() / 1.5));
    $('#resetView').click(() => graph && graph.zoomToFit(400));
    $('#refreshGraph').click(loadGraphData);
    
    // Display option handlers
    $('#showLabels').change(function() {
        if (graph) {
            // Force redraw when label visibility changes
            graph.refresh();
        }
    });
    
    $('#showLinks').change(function() {
        if (graph) {
            graph.linkVisibility(this.checked);
            graph.refresh();
        }
    });
});

function loadGraphData() {
    $('#loading-spinner').show();
    
    $.get('/api/graph-data')
        .done(function(response) {
            if (response.data) {
                graphData = response.data;
                initializeGraph(graphData);
                updateStatistics(graphData);
                updateLegend(graphData);
            } else {
                showError('No graph data available');
            }
        })
        .fail(function() {
            showError('Failed to load graph data');
        })
        .always(function() {
            $('#loading-spinner').hide();
        });
}

function initializeGraph(data) {
    // Clear existing graph
    $('#graph-container').empty();
    
    // Create new graph
    graph = ForceGraph()
        (document.getElementById('graph-container'))
        .graphData(data)
        .nodeId('id')
        .nodeLabel(node => node.name) // Ensure name is properly accessed
        .nodeColor(node => getNodeColor(node.category))
        .nodeVal('val')
        .linkSource('source')
        .linkTarget('target')
        .linkLabel('type')
        .linkDirectionalArrowLength(3)
        .linkDirectionalArrowRelPos(1)
        .linkColor(() => '#999')
        .linkWidth(link => Math.sqrt(link.value || 1))
        .linkVisibility($('#showLinks').is(':checked'))
        // Use nodeCanvasObjectMode 'after' to preserve the default rendering of links
        .nodeCanvasObjectMode(() => 'after')
        // Add node canvas object to improve label visibility
        .nodeCanvasObject((node, ctx, globalScale) => {
            // Don't draw anything if node doesn't have a position yet
            if (node.x === undefined || node.y === undefined) return;
            
            const nodeColor = getNodeColor(node.category);
            const size = Math.max(3, node.val || 1) * 4;
            
            // Draw the label if show labels is checked
            if ($('#showLabels').is(':checked') && node.name) {
                // Display name directly on the node
                const fontSize = 10/globalScale;
                ctx.font = `bold ${fontSize}px Sans-Serif`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                
                // Add background for better visibility
                const textWidth = ctx.measureText(node.name).width;
                const bgPadding = 2;
                
                ctx.fillStyle = 'rgba(255, 255, 255, 0.85)';
                ctx.fillRect(
                    node.x - textWidth/2 - bgPadding,
                    node.y - fontSize/2 - bgPadding,
                    textWidth + bgPadding * 2,
                    fontSize + bgPadding * 2
                );
                
                // Draw text on the background
                ctx.fillStyle = '#000';
                ctx.fillText(node.name, node.x, node.y);
            }
        })
        .onNodeClick(showNodeDetails)
        .onNodeHover(node => {
            document.body.style.cursor = node ? 'pointer' : null;
        })
        .cooldownTicks(100)
        .onEngineStop(() => graph.zoomToFit(400));
}

function getNodeColor(category) {
    // Enhanced color palette with more distinct colors for better visibility
    const colors = {
        'person': '#e74c3c',          // Bright red
        'organization': '#3498db',    // Bright blue
        'location': '#2ecc71',        // Bright green
        'event': '#9b59b6',           // Purple
        'concept': '#f1c40f',         // Yellow
        'technology': '#1abc9c',      // Teal
        'product': '#e67e22',         // Orange
        'date': '#34495e',            // Dark blue
        'document': '#8e44ad',        // Dark purple
        'number': '#d35400',          // Dark orange
        'time': '#16a085',           // Dark green
        'money': '#27ae60',          // Medium green
        'misc': '#f39c12',           // Gold
        'unknown': '#95a5a6'          // Gray
    };
    
    // If we don't have a predefined color, generate one based on the category string
    if (!colors[category]) {
        // Use string hashing to generate a consistent color for the same category
        let hash = 0;
        for (let i = 0; i < category.length; i++) {
            hash = category.charCodeAt(i) + ((hash << 5) - hash);
        }
        
        // Generate HSL color with good saturation and lightness for visibility
        const h = Math.abs(hash) % 360;
        return `hsl(${h}, 70%, 60%)`;
    }
    
    return colors[category];
}

function showNodeDetails(node) {
    $('#modalNodeName').text(node.name);
    $('#modalNodeCategory').text(node.category || 'Unknown');
    $('#modalNodeConnections').text(node.val + ' connections');
    $('#nodeModal').modal('show');
}

function updateStatistics(data) {
    $('#nodeCount').text(data.nodes.length);
    $('#linkCount').text(data.links.length);
    
    // Category statistics
    const categories = {};
    data.nodes.forEach(node => {
        const cat = node.category || 'unknown';
        categories[cat] = (categories[cat] || 0) + 1;
    });
    
    let categoryHtml = '<h6>Categories:</h6>';
    Object.entries(categories).forEach(([category, count]) => {
        categoryHtml += `
            <div class="d-flex justify-content-between">
                <span>${category.charAt(0).toUpperCase() + category.slice(1)}:</span>
                <span class="badge bg-secondary">${count}</span>
            </div>
        `;
    });
    
    $('#categoryStats').html(categoryHtml);
}

function updateLegend(data) {
    const categories = [...new Set(data.nodes.map(node => node.category || 'unknown'))];
    
    // Sort categories alphabetically for consistent display
    categories.sort((a, b) => a.localeCompare(b));
    
    let legendHtml = '<div class="d-flex flex-wrap">';
    categories.forEach(category => {
        const color = getNodeColor(category);
        const count = data.nodes.filter(node => (node.category || 'unknown') === category).length;
        legendHtml += `
            <div class="legend-item">
                <span class="legend-color" style="background-color: ${color}"></span>
                <span class="fw-bold">${category.charAt(0).toUpperCase() + category.slice(1)}</span>
                <span class="badge rounded-pill ms-1" style="background-color: ${color}; color: #fff; font-size: 0.75rem;">${count}</span>
            </div>
        `;
    });
    legendHtml += '</div>';
    
    // Add relationship line to legend
    if (data.links.length > 0) {
        legendHtml += `
            <hr>
            <div class="mt-2">
                <h6 class="mb-2">Relationships</h6>
                <div class="legend-item" style="width: auto;">
                    <svg width="30" height="2" style="vertical-align: middle;">
                        <line x1="0" y1="1" x2="30" y2="1" stroke="#999" stroke-width="2"/>
                    </svg>
                    <span class="ms-1">Connection between entities</span>
                    <span class="badge rounded-pill ms-1 bg-secondary">${data.links.length}</span>
                </div>
            </div>
        `;
    }
    
    $('#legend').html(legendHtml);
}

function showError(message) {
    $('#graph-container').html(`
        <div class="text-center py-5">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h5>Graph Loading Error</h5>
            <p class="text-muted">${message}</p>
            <button class="btn btn-primary" onclick="loadGraphData()">
                <i class="fas fa-retry me-2"></i>Try Again
            </button>
        </div>
    `);
}
</script>
{% endblock %}
