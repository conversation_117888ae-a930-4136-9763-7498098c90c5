{% extends "base.html" %}

{% block title %}Documents - KAG Document Processing{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-file-alt me-2"></i>Document Management
            </h1>
            <a href="{{ url_for('upload_page') }}" class="btn btn-primary">
                <i class="fas fa-upload me-2"></i>Upload New Document
            </a>
        </div>
    </div>
</div>

{% if documents %}
<!-- Documents Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>All Documents ({{ documents|length }})
                        </h5>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchFilter" 
                                   placeholder="Filter documents...">
                            <span class="input-group-text">
                                <i class="fas fa-filter"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="documentsTable">
                        <thead>
                            <tr>
                                <th>Document</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Chunks</th>
                                <th>Entities</th>
                                <th>Relationships</th>
                                <th>Uploaded</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for doc in documents %}
                            <tr data-doc-id="{{ doc.doc_id }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-{{ 'pdf' if doc.file_type == 'pdf' else 'video' }} me-2 text-primary"></i>
                                        <div>
                                            <div class="fw-bold">{{ doc.title }}</div>
                                            <small class="text-muted">ID: {{ doc.doc_id }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ doc.file_type.upper() }}</span>
                                </td>
                                <td>
                                    {% if doc.status == 'completed' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>Completed
                                        </span>
                                    {% elif doc.status == 'failed' %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>Failed
                                        </span>
                                    {% elif doc.status in ['started', 'chunking', 'embeddings', 'entities', 'relationships'] %}
                                        <span class="badge bg-warning processing-status">
                                            <i class="fas fa-spinner fa-spin me-1"></i>Processing
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ doc.status.title() }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ doc.total_chunks }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-warning">{{ doc.total_entities }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ doc.total_relationships }}</span>
                                </td>
                                <td>
                                    {% if doc.created_at %}
                                        <small>{{ doc.created_at[:10] }}</small>
                                    {% else %}
                                        <small class="text-muted">N/A</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('document_detail', doc_id=doc.doc_id) }}" 
                                           class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('processing_status', doc_id=doc.doc_id) }}" 
                                           class="btn btn-outline-info" title="Processing Status">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        {% if doc.status not in ['completed', 'failed'] %}
                                        <button class="btn btn-outline-danger cancel-btn" 
                                                data-doc-id="{{ doc.doc_id }}" title="Cancel Processing">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Document Statistics -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                <h5>{{ documents|length }}</h5>
                <p class="card-text text-muted">Total Documents</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h5>{{ documents|selectattr('status', 'equalto', 'completed')|list|length }}</h5>
                <p class="card-text text-muted">Completed</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-spinner fa-2x text-warning mb-2"></i>
                <h5>{{ documents|rejectattr('status', 'in', ['completed', 'failed'])|list|length }}</h5>
                <p class="card-text text-muted">Processing</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                <h5>{{ documents|selectattr('status', 'equalto', 'failed')|list|length }}</h5>
                <p class="card-text text-muted">Failed</p>
            </div>
        </div>
    </div>
</div>

{% else %}
<!-- No Documents -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-inbox fa-4x text-muted mb-4"></i>
                <h4>No Documents Found</h4>
                <p class="text-muted mb-4">
                    You haven't uploaded any documents yet. Start by uploading your first document to build your knowledge base.
                </p>
                <a href="{{ url_for('upload_page') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-upload me-2"></i>Upload Your First Document
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Cancel Processing Modal -->
<div class="modal fade" id="cancelModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-stop me-2"></i>Cancel Processing
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel processing for this document?</p>
                <p class="text-muted">This action cannot be undone. You'll need to re-upload the document to process it again.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Keep Processing</button>
                <button type="button" class="btn btn-danger" id="confirmCancel">Cancel Processing</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    let docIdToCancel = null;
    
    // Search filter functionality
    $('#searchFilter').on('keyup', function() {
        const value = $(this).val().toLowerCase();
        $('#documentsTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
    
    // Cancel processing button handler
    $('.cancel-btn').click(function() {
        docIdToCancel = $(this).data('doc-id');
        $('#cancelModal').modal('show');
    });
    
    // Confirm cancel processing
    $('#confirmCancel').click(function() {
        if (docIdToCancel) {
            // Create a form and submit it
            const form = $('<form>', {
                method: 'POST',
                action: `/cancel/${docIdToCancel}`
            });
            $('body').append(form);
            form.submit();
        }
    });
    
    // Auto-refresh processing status every 10 seconds
    setInterval(function() {
        $('.processing-status').each(function() {
            const row = $(this).closest('tr');
            const docId = row.data('doc-id');
            
            // Make AJAX call to get updated status
            $.get(`/api/status/${docId}`)
                .done(function(data) {
                    if (data.currentStep === 'completed') {
                        location.reload(); // Reload to show updated stats
                    } else if (data.currentStep === 'failed') {
                        location.reload(); // Reload to show failed status
                    }
                })
                .fail(function() {
                    console.log('Failed to update status for doc', docId);
                });
        });
    }, 10000);
    
    // Sort table functionality
    $('th').click(function() {
        const table = $(this).parents('table').eq(0);
        const rows = table.find('tr:gt(0)').toArray().sort(comparer($(this).index()));
        this.asc = !this.asc;
        if (!this.asc) {
            rows.reverse();
        }
        for (let i = 0; i < rows.length; i++) {
            table.append(rows[i]);
        }
    });
    
    function comparer(index) {
        return function(a, b) {
            const valA = getCellValue(a, index);
            const valB = getCellValue(b, index);
            return $.isNumeric(valA) && $.isNumeric(valB) ? valA - valB : valA.toString().localeCompare(valB);
        };
    }
    
    function getCellValue(row, index) {
        return $(row).children('td').eq(index).text();
    }
});
</script>
{% endblock %}
