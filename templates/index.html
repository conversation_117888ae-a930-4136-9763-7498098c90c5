{% extends "base.html" %}

{% block title %}Dashboard - KAG Document Processing{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
{% if kb_data and kb_data.stats %}
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                <h5 class="card-title">{{ kb_data.stats.total_documents }}</h5>
                <p class="card-text text-muted">Total Documents</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-puzzle-piece fa-2x text-success mb-2"></i>
                <h5 class="card-title">{{ kb_data.stats.total_chunks }}</h5>
                <p class="card-text text-muted">Document Chunks</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-tags fa-2x text-warning mb-2"></i>
                <h5 class="card-title">{{ kb_data.stats.total_entities }}</h5>
                <p class="card-text text-muted">Entities</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-link fa-2x text-info mb-2"></i>
                <h5 class="card-title">{{ kb_data.stats.total_relationships }}</h5>
                <p class="card-text text-muted">Relationships</p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('upload_page') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-upload me-2"></i>Upload Document
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('search_page') }}" class="btn btn-success btn-lg w-100">
                            <i class="fas fa-search me-2"></i>Search Documents
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('documents_page') }}" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-file-alt me-2"></i>Manage Documents
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('graph_page') }}" class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-project-diagram me-2"></i>Knowledge Graph
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Documents -->
{% if kb_data and kb_data.stats and kb_data.stats.documents %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Documents
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Document</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Chunks</th>
                                <th>Entities</th>
                                <th>Uploaded</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for doc in kb_data.stats.documents[:10] %}
                            <tr>
                                <td>
                                    <i class="fas fa-file-{{ 'pdf' if doc.file_type == 'pdf' else 'video' }} me-2"></i>
                                    {{ doc.title }}
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ doc.file_type.upper() }}</span>
                                </td>
                                <td>
                                    {% if doc.status == 'completed' %}
                                        <span class="badge bg-success">Completed</span>
                                    {% elif doc.status == 'failed' %}
                                        <span class="badge bg-danger">Failed</span>
                                    {% else %}
                                        <span class="badge bg-warning processing-status">Processing</span>
                                    {% endif %}
                                </td>
                                <td>{{ doc.total_chunks }}</td>
                                <td>{{ doc.total_entities }}</td>
                                <td>
                                    {% if doc.created_at %}
                                        {{ doc.created_at[:10] }}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('document_detail', doc_id=doc.doc_id) }}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('processing_status', doc_id=doc.doc_id) }}" 
                                           class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if kb_data.stats.documents|length > 10 %}
                <div class="text-center mt-3">
                    <a href="{{ url_for('documents_page') }}" class="btn btn-outline-primary">
                        View All Documents
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5>No Documents Yet</h5>
                <p class="text-muted">Upload your first document to get started with the knowledge graph system.</p>
                <a href="{{ url_for('upload_page') }}" class="btn btn-primary">
                    <i class="fas fa-upload me-2"></i>Upload Document
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh processing status every 5 seconds
setInterval(function() {
    $('.processing-status').each(function() {
        // You could add AJAX calls here to update status in real-time
    });
}, 5000);
</script>
{% endblock %}
