{% extends "base.html" %}

{% block title %}Search Results - KAG Document Processing{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-search me-2"></i>Search Results
        </h1>
    </div>
</div>

<!-- Search Query Info -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1">Search Query:</h6>
                        <p class="mb-0 text-muted">"{{ query }}"</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <small class="text-muted">
                            Found {{ results|length }} results in {{ "%.2f"|format(execution_time) }}s
                        </small>
                        <br>
                        <a href="{{ url_for('search_page') }}" class="btn btn-outline-primary btn-sm mt-2">
                            <i class="fas fa-search me-1"></i>New Search
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Limited Mode Warning -->
{% if limited_mode is defined and limited_mode %}
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Limited Search Mode:</strong> Search is operating with reduced capabilities due to AI service issues. Results may be less accurate and AI-generated responses may be limited.
        </div>
    </div>
</div>
{% endif %}

<!-- Debug Information Section -->
{% if debug_info is defined and debug_info %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-bug me-2"></i>Search Debug Information
                </h5>
                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="collapse" 
                        data-bs-target="#debugInfoCollapse" aria-expanded="false">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
            <div class="collapse" id="debugInfoCollapse">
                <div class="card-body">
                    <!-- Summary Metrics -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2">Overall Search Metrics</h6>
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body py-2 px-3">
                                            <h6 class="text-muted mb-1">Tokens Used</h6>
                                            <h5>{{ debug_info.total_tokens_used }}</h5>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body py-2 px-3">
                                            <h6 class="text-muted mb-1">Chunks Read</h6>
                                            <h5>{{ debug_info.total_chunks_read }}</h5>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body py-2 px-3">
                                            <h6 class="text-muted mb-1">Entities Accessed</h6>
                                            <h5>{{ debug_info.total_entities_accessed }}</h5>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body py-2 px-3">
                                            <h6 class="text-muted mb-1">Relationships</h6>
                                            <h5>{{ debug_info.total_relationships_traversed }}</h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step by Step Process -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2">Search Process Steps</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>Step</th>
                                            <th>Duration (s)</th>
                                            <th>Description</th>
                                            <th>Details</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for step in debug_info.steps %}
                                        <tr>
                                            <td>
                                                <span class="badge {% if 'error' in step.step_name %}bg-danger{% else %}bg-info{% endif %}">
                                                    {{ step.step_name }}
                                                </span>
                                            </td>
                                            <td>{{ "%.3f"|format(step.duration_seconds) }}</td>
                                            <td>{{ step.description }}</td>
                                            <td>
                                                {% if step.additional_info %}
                                                <button class="btn btn-sm btn-outline-secondary" type="button" 
                                                        data-bs-toggle="collapse" data-bs-target="#step{{ loop.index }}Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <div class="collapse mt-2" id="step{{ loop.index }}Details">
                                                    <div class="card card-body py-2 px-3 bg-light">
                                                        <pre class="mb-0"><code>{{ step.additional_info|tojson(indent=2) }}</code></pre>
                                                    </div>
                                                </div>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Component Details -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2">Search Component Details</h6>
                            <div class="row">
                                <!-- Vector Search Stats -->
                                {% if debug_info.vector_search_stats %}
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Vector Search</h6>
                                        </div>
                                        <div class="card-body">
                                            <pre><code>{{ debug_info.vector_search_stats|tojson(indent=2) }}</code></pre>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                
                                <!-- Text Search Stats -->
                                {% if debug_info.text_search_stats %}
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Text Search</h6>
                                        </div>
                                        <div class="card-body">
                                            <pre><code>{{ debug_info.text_search_stats|tojson(indent=2) }}</code></pre>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                
                                <!-- Graph Traversal Stats -->
                                {% if debug_info.graph_traversal_stats %}
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Graph Traversal</h6>
                                        </div>
                                        <div class="card-body">
                                            <pre><code>{{ debug_info.graph_traversal_stats|tojson(indent=2) }}</code></pre>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                
                                <!-- Response Generation Stats -->
                                {% if debug_info.response_generation_stats %}
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Response Generation</h6>
                                        </div>
                                        <div class="card-body">
                                            <pre><code>{{ debug_info.response_generation_stats|tojson(indent=2) }}</code></pre>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Generated Response -->
{% if generated_response %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-robot me-2"></i>AI Generated Response
                </h5>
            </div>
            <div class="card-body">
                <div class="generated-response">
                    {{ generated_response|safe }}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Search Results -->
{% if results %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Relevant Document Chunks
                </h5>
            </div>
            <div class="card-body">
                {% for result in results %}
                <div class="search-result mb-4 {% if not loop.last %}border-bottom pb-4{% endif %}">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="text-primary">
                                <i class="fas fa-file-alt me-2"></i>
                                Document ID: {{ result.doc_id }}
                                {% if result.score %}
                                    <span class="badge bg-success ms-2">
                                        Score: {{ "%.3f"|format(result.score) }}
                                    </span>
                                {% endif %}
                            </h6>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('document_detail', doc_id=result.doc_id) }}"
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View Document
                            </a>
                        </div>
                    </div>

                    <div class="chunk-content mt-3">
                        <div class="content-text">
                            {{ result.content }}
                        </div>

                        {% if result.metadata %}
                        <div class="metadata mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                {% for key, value in result.metadata.items() %}
                                    <strong>{{ key.title() }}:</strong> {{ value }}
                                    {% if not loop.last %} | {% endif %}
                                {% endfor %}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5>No Results Found</h5>
                <p class="text-muted">
                    No documents match your search query. Try using different keywords or phrases.
                </p>
                <a href="{{ url_for('search_page') }}" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>Try Another Search
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Search Tips -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Search Tips
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Use natural language questions</li>
                            <li><i class="fas fa-check text-success me-2"></i>Be specific about what you're looking for</li>
                            <li><i class="fas fa-check text-success me-2"></i>Try different phrasings if no results</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Include context in your queries</li>
                            <li><i class="fas fa-check text-success me-2"></i>Use synonyms for better coverage</li>
                            <li><i class="fas fa-check text-success me-2"></i>Check document details for more info</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.generated-response {
    font-size: 1.1rem;
    line-height: 1.6;
    white-space: pre-wrap;
}

.search-result {
    transition: all 0.3s ease;
}

.search-result:hover {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    margin: -1rem;
}

.content-text {
    font-size: 0.95rem;
    line-height: 1.5;
    max-height: 200px;
    overflow-y: auto;
}

.metadata {
    border-top: 1px solid #dee2e6;
    padding-top: 0.5rem;
}

.chunk-content {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.375rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Highlight search terms in results
    const query = "{{ query }}";
    if (query) {
        const terms = query.toLowerCase().split(' ').filter(term => term.length > 2);

        $('.content-text').each(function() {
            let content = $(this).html();
            terms.forEach(term => {
                const regex = new RegExp(`(${term})`, 'gi');
                content = content.replace(regex, '<mark>$1</mark>');
            });
            $(this).html(content);
        });
    }

    // Smooth scroll to results
    if ($('.search-result').length > 0) {
        $('html, body').animate({
            scrollTop: $('.search-result').first().offset().top - 100
        }, 500);
    }
});
</script>
{% endblock %}
