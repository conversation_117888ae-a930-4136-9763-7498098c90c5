{% extends "base.html" %}

{% block title %}Search Results - KAG Document Processing{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-search me-2"></i>Search Results
        </h1>
    </div>
</div>

<!-- Search Query Info -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1">Search Query:</h6>
                        <p class="mb-0 text-muted">"{{ query }}"</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <small class="text-muted">
                            Found {{ results|length }} results in {{ "%.2f"|format(execution_time) }}s
                        </small>
                        <br>
                        <a href="{{ url_for('search_page') }}" class="btn btn-outline-primary btn-sm mt-2">
                            <i class="fas fa-search me-1"></i>New Search
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Limited Mode Warning -->
{% if limited_mode is defined and limited_mode %}
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Limited Search Mode:</strong> Search is operating with reduced capabilities due to AI service issues. Results may be less accurate and AI-generated responses may be limited.
        </div>
    </div>
</div>
{% endif %}

<!-- Debug Information Section -->
{% if debug_info is defined and debug_info %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-bug me-2"></i>Search Debug Information
                </h5>
                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="collapse" 
                        data-bs-target="#debugInfoCollapse" aria-expanded="false">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
            <div class="collapse" id="debugInfoCollapse">
                <div class="card-body">
                    <!-- Summary Metrics -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2">Overall Search Metrics</h6>
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body py-2 px-3">
                                            <h6 class="text-muted mb-1">Tokens Used</h6>
                                            <h5>{{ debug_info.total_tokens_used }}</h5>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body py-2 px-3">
                                            <h6 class="text-muted mb-1">Chunks Read</h6>
                                            <h5>{{ debug_info.total_chunks_read }}</h5>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body py-2 px-3">
                                            <h6 class="text-muted mb-1">Entities Accessed</h6>
                                            <h5>{{ debug_info.total_entities_accessed }}</h5>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="card bg-light">
                                        <div class="card-body py-2 px-3">
                                            <h6 class="text-muted mb-1">Relationships</h6>
                                            <h5>{{ debug_info.total_relationships_traversed }}</h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step by Step Process -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2">Search Process Steps</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>Step</th>
                                            <th>Duration (s)</th>
                                            <th>Description</th>
                                            <th>Details</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for step in debug_info.steps %}
                                        <tr>
                                            <td>
                                                <span class="badge {% if 'error' in step.step_name %}bg-danger{% else %}bg-info{% endif %}">
                                                    {{ step.step_name }}
                                                </span>
                                            </td>
                                            <td>{{ "%.3f"|format(step.duration_seconds) }}</td>
                                            <td>{{ step.description }}</td>
                                            <td>
                                                {% if step.additional_info %}
                                                <button class="btn btn-sm btn-outline-secondary" type="button" 
                                                        data-bs-toggle="collapse" data-bs-target="#step{{ loop.index }}Details">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                                <div class="collapse mt-2" id="step{{ loop.index }}Details">
                                                    <div class="card card-body py-2 px-3 bg-light">
                                                        <pre class="mb-0"><code>{{ step.additional_info|tojson(indent=2) }}</code></pre>
                                                    </div>
                                                </div>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Component Details -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2">Search Component Details</h6>
                            <div class="row">
                                <!-- Vector Search Stats -->
                                {% if debug_info.vector_search_stats %}
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Vector Search</h6>
                                        </div>
                                        <div class="card-body">
                                            <pre><code>{{ debug_info.vector_search_stats|tojson(indent=2) }}</code></pre>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                
                                <!-- Text Search Stats -->
                                {% if debug_info.text_search_stats %}
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Text Search</h6>
                                        </div>
                                        <div class="card-body">
                                            <pre><code>{{ debug_info.text_search_stats|tojson(indent=2) }}</code></pre>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                
                                <!-- Graph Traversal Stats -->
                                {% if debug_info.graph_traversal_stats %}
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Graph Traversal</h6>
                                        </div>
                                        <div class="card-body">
                                            <pre><code>{{ debug_info.graph_traversal_stats|tojson(indent=2) }}</code></pre>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                                
                                <!-- Response Generation Stats -->
                                {% if debug_info.response_generation_stats %}
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Response Generation</h6>
                                        </div>
                                        <div class="card-body">
                                            <pre><code>{{ debug_info.response_generation_stats|tojson(indent=2) }}</code></pre>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Search Flow Visualization -->
{% if search_flow is defined and search_flow %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-success">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-route me-2"></i>Search Flow Analysis
                </h5>
                <button class="btn btn-sm btn-light" type="button" data-bs-toggle="collapse"
                        data-bs-target="#searchFlowCollapse" aria-expanded="true">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
            <div class="collapse show" id="searchFlowCollapse">
                <div class="card-body">
                    <!-- Query Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2">
                                <i class="fas fa-info-circle me-2"></i>Query Analysis
                            </h6>
                            <div class="row">
                                <div class="col-md-8">
                                    <p><strong>Query:</strong> "{{ search_flow.query }}"</p>
                                    {% if search_flow.chinese_analysis %}
                                    <p><strong>Language:</strong> Chinese</p>
                                    <p><strong>Translation:</strong> {{ search_flow.chinese_analysis.query_translation }}</p>
                                    {% else %}
                                    <p><strong>Language:</strong> English</p>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <p><strong>Total Duration:</strong> {{ "%.3f"|format(search_flow.total_duration) }}s</p>
                                    <p><strong>Results Found:</strong> {{ search_flow.statistics.results_count or 0 }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search Process Flow -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2">
                                <i class="fas fa-cogs me-2"></i>Search Process Steps
                            </h6>
                            <div class="search-flow-timeline">
                                {% for stage in search_flow.stages %}
                                <div class="timeline-item">
                                    <div class="timeline-marker">
                                        {% if stage.status == 'completed' %}
                                        <i class="fas fa-check-circle text-success"></i>
                                        {% elif stage.status == 'no_results' %}
                                        <i class="fas fa-exclamation-circle text-warning"></i>
                                        {% else %}
                                        <i class="fas fa-times-circle text-danger"></i>
                                        {% endif %}
                                    </div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">{{ stage.name }}</h6>
                                        <p class="mb-1 text-muted">{{ stage.description }}</p>
                                        <small class="text-secondary">{{ stage.details }}</small>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <!-- Search Statistics -->
                    {% if search_flow.statistics %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2">
                                <i class="fas fa-chart-bar me-2"></i>Search Performance Metrics
                            </h6>
                            <div class="row">
                                <!-- Vector Search Stats -->
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light h-100">
                                        <div class="card-header bg-primary text-white py-2">
                                            <h6 class="mb-0">Vector Search</h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <p class="mb-1"><strong>Best Score:</strong> {{ "%.4f"|format(search_flow.statistics.vector_scores.max) }}</p>
                                            <p class="mb-1"><strong>Average:</strong> {{ "%.4f"|format(search_flow.statistics.vector_scores.avg) }}</p>
                                            <p class="mb-0"><strong>Range:</strong> {{ "%.4f"|format(search_flow.statistics.vector_scores.min) }} - {{ "%.4f"|format(search_flow.statistics.vector_scores.max) }}</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Text Search Stats -->
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light h-100">
                                        <div class="card-header bg-info text-white py-2">
                                            <h6 class="mb-0">Text Search</h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <p class="mb-1"><strong>Best Score:</strong> {{ "%.4f"|format(search_flow.statistics.text_scores.max) }}</p>
                                            <p class="mb-1"><strong>Average:</strong> {{ "%.4f"|format(search_flow.statistics.text_scores.avg) }}</p>
                                            <p class="mb-0"><strong>Range:</strong> {{ "%.4f"|format(search_flow.statistics.text_scores.min) }} - {{ "%.4f"|format(search_flow.statistics.text_scores.max) }}</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Knowledge Graph Stats -->
                                <div class="col-md-4 mb-3">
                                    <div class="card bg-light h-100">
                                        <div class="card-header bg-success text-white py-2">
                                            <h6 class="mb-0">Knowledge Graph</h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <p class="mb-1"><strong>Entities:</strong> {{ search_flow.statistics.knowledge_graph.total_entities }}</p>
                                            <p class="mb-1"><strong>Relationships:</strong> {{ search_flow.statistics.knowledge_graph.total_relationships }}</p>
                                            <p class="mb-0"><strong>Avg/Result:</strong> {{ "%.1f"|format(search_flow.statistics.knowledge_graph.avg_entities_per_result) }} entities</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Chinese Query Analysis -->
                    {% if search_flow.chinese_analysis %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2">
                                <i class="fas fa-language me-2"></i>Chinese Query Analysis
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body py-2">
                                            <h6 class="text-muted mb-1">Chinese Content Results</h6>
                                            <h5>{{ search_flow.chinese_analysis.chinese_content_results }}/{{ search_flow.statistics.results_count }}
                                                <small class="text-muted">({{ "%.1f"|format(search_flow.chinese_analysis.chinese_content_percentage) }}%)</small>
                                            </h5>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body py-2">
                                            <h6 class="text-muted mb-1">Stock Code Results</h6>
                                            <h5>{{ search_flow.chinese_analysis.stock_code_results }}/{{ search_flow.statistics.results_count }}
                                                <small class="text-muted">({{ "%.1f"|format(search_flow.chinese_analysis.stock_code_percentage) }}%)</small>
                                            </h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Reranking Flow Analysis -->
                    {% if search_flow.reranking_flow and search_flow.reranking_flow.enabled %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="border-bottom pb-2">
                                <i class="fas fa-sort-amount-up me-2"></i>Advanced Reranking Analysis
                            </h6>

                            <!-- Query Analysis -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="card bg-light">
                                        <div class="card-header bg-secondary text-white py-2">
                                            <h6 class="mb-0">Query Analysis</h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <p class="mb-1"><strong>Language:</strong> {{ search_flow.reranking_flow.query_analysis.language_detected }}</p>
                                                </div>
                                                <div class="col-md-3">
                                                    <p class="mb-1"><strong>Query Length:</strong> {{ search_flow.reranking_flow.query_analysis.query_length }} chars</p>
                                                </div>
                                                <div class="col-md-3">
                                                    <p class="mb-1"><strong>Financial Query:</strong> {{ "Yes" if search_flow.reranking_flow.query_analysis.is_financial_query else "No" }}</p>
                                                </div>
                                                <div class="col-md-3">
                                                    <p class="mb-1"><strong>Chinese Query:</strong> {{ "Yes" if search_flow.reranking_flow.query_analysis.is_chinese_query else "No" }}</p>
                                                </div>
                                            </div>
                                            {% if search_flow.reranking_flow.query_analysis.detected_financial_terms %}
                                            <p class="mb-0"><strong>Financial Terms:</strong> {{ search_flow.reranking_flow.query_analysis.detected_financial_terms|join(', ') }}</p>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Factor Weights -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="card bg-light">
                                        <div class="card-header bg-info text-white py-2">
                                            <h6 class="mb-0">Reranking Factor Weights</h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <div class="row">
                                                {% for factor, weight in search_flow.reranking_flow.factor_weights.items() %}
                                                <div class="col-md-3 mb-2">
                                                    <div class="d-flex justify-content-between">
                                                        <span>{{ factor.replace('_', ' ').title() }}:</span>
                                                        <strong>{{ "%.1f"|format(weight * 100) }}%</strong>
                                                    </div>
                                                    <div class="progress" style="height: 4px;">
                                                        <div class="progress-bar" role="progressbar" style="width: {{ weight * 100 }}%"></div>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Performance Metrics -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-header bg-success text-white py-2">
                                            <h6 class="mb-0">Performance Metrics</h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <p class="mb-1"><strong>Results Processed:</strong> {{ search_flow.reranking_flow.performance_metrics.total_results_processed }}</p>
                                            <p class="mb-1"><strong>Avg Score Improvement:</strong> {{ "%.4f"|format(search_flow.reranking_flow.performance_metrics.avg_score_improvement) }}</p>
                                            <p class="mb-0"><strong>Score Range:</strong> {{ "%.3f"|format(search_flow.reranking_flow.performance_metrics.reranked_score_range.min) }} - {{ "%.3f"|format(search_flow.reranking_flow.performance_metrics.reranked_score_range.max) }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-header bg-warning text-white py-2">
                                            <h6 class="mb-0">Optimization Summary</h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <p class="mb-1"><strong>Top Factor:</strong> {{ search_flow.reranking_flow.summary.top_contributing_factor.replace('_', ' ').title() }}</p>
                                            <p class="mb-1"><strong>Language:</strong> {{ search_flow.reranking_flow.summary.language_optimization }}</p>
                                            <p class="mb-0"><strong>Domain:</strong> {{ search_flow.reranking_flow.summary.domain_optimization }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Individual Result Analysis -->
                            {% if search_flow.reranking_flow.results_analysis %}
                            <div class="row">
                                <div class="col-12">
                                    <div class="card bg-light">
                                        <div class="card-header bg-dark text-white py-2 d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">Individual Result Reranking Details</h6>
                                            <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="collapse"
                                                    data-bs-target="#rerankingDetailsCollapse" aria-expanded="false">
                                                <i class="fas fa-chevron-down"></i>
                                            </button>
                                        </div>
                                        <div class="collapse" id="rerankingDetailsCollapse">
                                            <div class="card-body py-2">
                                                {% for result_analysis in search_flow.reranking_flow.results_analysis[:3] %}
                                                <div class="mb-3 p-3 border rounded">
                                                    <h6 class="mb-2">Document {{ result_analysis.doc_id }}</h6>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <p class="mb-1"><strong>Original Score:</strong> {{ "%.4f"|format(result_analysis.original_scores.combined_score) }}</p>
                                                            <p class="mb-1"><strong>Reranked Score:</strong> {{ "%.4f"|format(result_analysis.final_rerank_score) }}</p>
                                                            <p class="mb-0"><strong>Improvement:</strong>
                                                                <span class="{{ 'text-success' if result_analysis.score_improvement > 0 else 'text-danger' if result_analysis.score_improvement < 0 else 'text-muted' }}">
                                                                    {{ "%.4f"|format(result_analysis.score_improvement) }}
                                                                </span>
                                                            </p>
                                                        </div>
                                                        <div class="col-md-8">
                                                            <div class="row">
                                                                {% for factor, score in result_analysis.reranking_factors.items() %}
                                                                <div class="col-md-6 mb-1">
                                                                    <small>{{ factor.replace('_', ' ').title() }}: {{ "%.3f"|format(score) }}</small>
                                                                </div>
                                                                {% endfor %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mt-2">
                                                        <small class="text-muted">{{ result_analysis.content_preview }}</small>
                                                    </div>
                                                </div>
                                                {% endfor %}
                                                {% if search_flow.reranking_flow.results_analysis|length > 3 %}
                                                <p class="text-muted text-center mb-0">... and {{ search_flow.reranking_flow.results_analysis|length - 3 }} more results</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Generated Response -->
{% if generated_response %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-robot me-2"></i>AI Generated Response
                </h5>
            </div>
            <div class="card-body">
                <div class="generated-response">
                    {{ generated_response|safe }}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Search Results -->
{% if results %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Relevant Document Chunks
                </h5>
            </div>
            <div class="card-body">
                {% for result in results %}
                <div class="search-result mb-4 {% if not loop.last %}border-bottom pb-4{% endif %}">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="text-primary">
                                <i class="fas fa-file-alt me-2"></i>
                                Document ID: {{ result.doc_id }}
                                {% if result.score %}
                                    <span class="badge bg-success ms-2">
                                        Score: {{ "%.3f"|format(result.score) }}
                                    </span>
                                {% endif %}
                                {% if show_flow and (result.vector_score is defined or result.text_score is defined or result.combined_score is defined) %}
                                    <button class="btn btn-sm btn-outline-info ms-2" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#scores{{ loop.index }}"
                                            aria-expanded="false">
                                        <i class="fas fa-chart-line me-1"></i>Scores
                                    </button>
                                {% endif %}
                            </h6>

                            {% if show_flow and (result.vector_score is defined or result.text_score is defined or result.combined_score is defined) %}
                            <div class="collapse mt-2" id="scores{{ loop.index }}">
                                <div class="card card-body py-2 px-3 bg-light">
                                    <div class="row text-center">
                                        {% if result.vector_score is defined %}
                                        <div class="col-4">
                                            <small class="text-muted d-block">Vector Score</small>
                                            <strong class="text-primary">{{ "%.4f"|format(result.vector_score) }}</strong>
                                        </div>
                                        {% endif %}
                                        {% if result.text_score is defined %}
                                        <div class="col-4">
                                            <small class="text-muted d-block">Text Score</small>
                                            <strong class="text-info">{{ "%.4f"|format(result.text_score) }}</strong>
                                        </div>
                                        {% endif %}
                                        {% if result.combined_score is defined %}
                                        <div class="col-4">
                                            <small class="text-muted d-block">Combined Score</small>
                                            <strong class="text-success">{{ "%.4f"|format(result.combined_score) }}</strong>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="{{ url_for('document_detail', doc_id=result.doc_id) }}"
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>View Document
                            </a>
                        </div>
                    </div>

                    <div class="chunk-content mt-3">
                        <div class="content-text">
                            {{ result.content }}
                        </div>

                        {% if result.metadata %}
                        <div class="metadata mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                {% for key, value in result.metadata.items() %}
                                    <strong>{{ key.title() }}:</strong> {{ value }}
                                    {% if not loop.last %} | {% endif %}
                                {% endfor %}
                            </small>
                        </div>
                        {% endif %}

                        {% if show_flow and (result.entities or result.relationships) %}
                        <div class="knowledge-graph-info mt-3">
                            <div class="row">
                                {% if result.entities %}
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-2">
                                        <i class="fas fa-tags me-1"></i>Entities ({{ result.entities|length }})
                                    </h6>
                                    <div class="entities-list">
                                        {% for entity in result.entities[:5] %}
                                        <span class="badge bg-secondary me-1 mb-1">{{ entity.name }}</span>
                                        {% endfor %}
                                        {% if result.entities|length > 5 %}
                                        <span class="text-muted">... and {{ result.entities|length - 5 }} more</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if result.relationships %}
                                <div class="col-md-6">
                                    <h6 class="text-muted mb-2">
                                        <i class="fas fa-project-diagram me-1"></i>Relationships ({{ result.relationships|length }})
                                    </h6>
                                    <div class="relationships-list">
                                        {% for relationship in result.relationships[:3] %}
                                        <small class="d-block text-muted">
                                            <i class="fas fa-arrow-right me-1"></i>{{ relationship.relation_type }}
                                        </small>
                                        {% endfor %}
                                        {% if result.relationships|length > 3 %}
                                        <small class="text-muted">... and {{ result.relationships|length - 3 }} more</small>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5>No Results Found</h5>
                <p class="text-muted">
                    No documents match your search query. Try using different keywords or phrases.
                </p>
                <a href="{{ url_for('search_page') }}" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>Try Another Search
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Search Tips -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Search Tips
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Use natural language questions</li>
                            <li><i class="fas fa-check text-success me-2"></i>Be specific about what you're looking for</li>
                            <li><i class="fas fa-check text-success me-2"></i>Try different phrasings if no results</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Include context in your queries</li>
                            <li><i class="fas fa-check text-success me-2"></i>Use synonyms for better coverage</li>
                            <li><i class="fas fa-check text-success me-2"></i>Check document details for more info</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
.generated-response {
    font-size: 1.1rem;
    line-height: 1.6;
    white-space: pre-wrap;
}

.search-result {
    transition: all 0.3s ease;
}

.search-result:hover {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    margin: -1rem;
}

.content-text {
    font-size: 0.95rem;
    line-height: 1.5;
    max-height: 200px;
    overflow-y: auto;
}

.metadata {
    border-top: 1px solid #dee2e6;
    padding-top: 0.5rem;
}

.chunk-content {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 0.375rem;
}

/* Search Flow Timeline Styles */
.search-flow-timeline {
    position: relative;
    padding-left: 2rem;
}

.search-flow-timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #28a745, #17a2b8, #ffc107, #dc3545);
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: flex-start;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0.25rem;
    width: 2rem;
    height: 2rem;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1;
}

.timeline-marker i {
    font-size: 1rem;
}

.timeline-content {
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-left: 1rem;
    flex: 1;
    border-left: 3px solid #dee2e6;
    transition: all 0.3s ease;
}

.timeline-content:hover {
    background: #e9ecef;
    border-left-color: #007bff;
    transform: translateX(5px);
}

/* Search Flow Cards */
.search-flow-card {
    transition: transform 0.2s ease;
}

.search-flow-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Highlight search terms in results
    const query = "{{ query }}";
    if (query) {
        const terms = query.toLowerCase().split(' ').filter(term => term.length > 2);

        $('.content-text').each(function() {
            let content = $(this).html();
            terms.forEach(term => {
                const regex = new RegExp(`(${term})`, 'gi');
                content = content.replace(regex, '<mark>$1</mark>');
            });
            $(this).html(content);
        });
    }

    // Smooth scroll to results
    if ($('.search-result').length > 0) {
        $('html, body').animate({
            scrollTop: $('.search-result').first().offset().top - 100
        }, 500);
    }
});
</script>
{% endblock %}
