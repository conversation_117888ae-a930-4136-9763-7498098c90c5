{% extends "base.html" %}

{% block title %}Processing Status - KAG Document Processing{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-tasks me-2"></i>Processing Status
            </h1>
            <div>
                <button class="btn btn-outline-primary" onclick="location.reload()">
                    <i class="fas fa-sync-alt me-2"></i>Refresh
                </button>
                <a href="{{ url_for('documents_page') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Documents
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Status Overview -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Document Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Document ID:</strong> {{ doc_id }}</p>
                        {% if status %}
                        <p><strong>File Name:</strong> {{ status.fileName or 'N/A' }}</p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if status %}
                        <p><strong>Current Status:</strong> 
                            {% if status.currentStep == 'completed' %}
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-check me-1"></i>Completed
                                </span>
                            {% elif status.currentStep == 'failed' %}
                                <span class="badge bg-danger fs-6">
                                    <i class="fas fa-times me-1"></i>Failed
                                </span>
                            {% elif status.currentStep %}
                                <span class="badge bg-warning fs-6 processing-status">
                                    <i class="fas fa-spinner fa-spin me-1"></i>{{ status.currentStep.title() }}
                                </span>
                            {% else %}
                                <span class="badge bg-secondary fs-6">Unknown</span>
                            {% endif %}
                        </p>
                        {% endif %}
                        <p><strong>Last Updated:</strong> <span id="lastUpdated">{% if status and status.timestamp %}{{ status.timestamp }}{% else %}{{ current_time }}{% endif %}</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Processing Steps -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list-ol me-2"></i>Processing Pipeline
                </h5>
            </div>
            <div class="card-body">
                <div class="processing-steps">
                    {% set steps = [
                        ('started', 'Document Upload', 'fas fa-upload'),
                        ('chunking', 'Text Chunking', 'fas fa-puzzle-piece'),
                        ('embeddings', 'Generate Embeddings', 'fas fa-brain'),
                        ('entities', 'Extract Entities', 'fas fa-tags'),
                        ('relationships', 'Build Relationships', 'fas fa-link'),
                        ('completed', 'Processing Complete', 'fas fa-check-circle')
                    ] %}
                    
                    {% for step_key, step_name, step_icon in steps %}
                    <div class="step-item d-flex align-items-center mb-3">
                        <div class="step-icon me-3">
                            {% if status and status.currentStep %}
                                {% if step_key == status.currentStep %}
                                    <i class="{{ step_icon }} fa-2x text-warning"></i>
                                {% elif (steps|map(attribute=0)|list).index(step_key) < (steps|map(attribute=0)|list).index(status.currentStep) %}
                                    <i class="{{ step_icon }} fa-2x text-success"></i>
                                {% elif status.currentStep == 'completed' %}
                                    <i class="{{ step_icon }} fa-2x text-success"></i>
                                {% elif status.currentStep == 'failed' and step_key != 'completed' %}
                                    {% if (steps|map(attribute=0)|list).index(step_key) <= (steps|map(attribute=0)|list).index(status.currentStep) %}
                                        <i class="{{ step_icon }} fa-2x text-danger"></i>
                                    {% else %}
                                        <i class="{{ step_icon }} fa-2x text-muted"></i>
                                    {% endif %}
                                {% else %}
                                    <i class="{{ step_icon }} fa-2x text-muted"></i>
                                {% endif %}
                            {% else %}
                                <i class="{{ step_icon }} fa-2x text-muted"></i>
                            {% endif %}
                        </div>
                        <div class="step-content flex-grow-1">
                            <h6 class="mb-1">{{ step_name }}</h6>
                            <div class="step-description text-muted">
                                {% if step_key == 'started' %}
                                    Document uploaded and queued for processing
                                {% elif step_key == 'chunking' %}
                                    Breaking document into manageable chunks
                                {% elif step_key == 'embeddings' %}
                                    Creating vector embeddings using Mistral AI
                                {% elif step_key == 'entities' %}
                                    Identifying and extracting named entities
                                {% elif step_key == 'relationships' %}
                                    Building knowledge graph relationships
                                {% elif step_key == 'completed' %}
                                    Document ready for search and analysis
                                {% endif %}
                            </div>
                        </div>
                        <div class="step-status">
                            {% if status and status.currentStep %}
                                {% if step_key == status.currentStep %}
                                    <span class="badge bg-warning">In Progress</span>
                                {% elif (steps|map(attribute=0)|list).index(step_key) < (steps|map(attribute=0)|list).index(status.currentStep) %}
                                    <span class="badge bg-success">Completed</span>
                                {% elif status.currentStep == 'completed' %}
                                    <span class="badge bg-success">Completed</span>
                                {% elif status.currentStep == 'failed' %}
                                    {% if (steps|map(attribute=0)|list).index(step_key) <= (steps|map(attribute=0)|list).index(status.currentStep) %}
                                        <span class="badge bg-danger">Failed</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Pending</span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-secondary">Pending</span>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-secondary">Unknown</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Error Information -->
{% if status and status.errorMessage %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Error Details
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger mb-0">
                    <strong>Error Message:</strong><br>
                    <code>{{ status.errorMessage }}</code>
                </div>
                <div class="mt-3">
                    <h6>Troubleshooting Steps:</h6>
                    <ul>
                        <li>Check if the document format is supported</li>
                        <li>Ensure the file is not corrupted</li>
                        <li>Verify the document size is within limits</li>
                        <li>Try uploading the document again</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>Available Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if status and status.currentStep == 'completed' %}
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('document_detail', doc_id=doc_id) }}" class="btn btn-success w-100">
                            <i class="fas fa-eye me-2"></i>View Document Details
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('search_page') }}" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Search Documents
                        </a>
                    </div>
                    {% elif status and status.currentStep == 'failed' %}
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('upload_page') }}" class="btn btn-warning w-100">
                            <i class="fas fa-upload me-2"></i>Upload Again
                        </a>
                    </div>
                    {% elif status and status.currentStep not in ['completed', 'failed'] %}
                    <div class="col-md-4 mb-3">
                        <form method="POST" action="{{ url_for('cancel_processing', doc_id=doc_id) }}" 
                              onsubmit="return confirm('Are you sure you want to cancel processing?')">
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-stop me-2"></i>Cancel Processing
                            </button>
                        </form>
                    </div>
                    {% endif %}
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('documents_page') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-list me-2"></i>All Documents
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <button class="btn btn-outline-info w-100" onclick="location.reload()">
                            <i class="fas fa-sync-alt me-2"></i>Refresh Status
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Initialize variables
    var refreshInterval;
    
    // Auto-refresh status every 5 seconds if processing
    {% if status and status.currentStep not in ['completed', 'failed'] %}
    refreshInterval = setInterval(function() {
        $.get('/processing-status/{{ doc_id }}')
            .done(function(data) {
                if (data.currentStep === 'completed' || data.currentStep === 'failed') {
                    location.reload();
                }
                $('#lastUpdated').text(new Date().toLocaleString());
            })
            .fail(function() {
                console.log('Failed to refresh status');
            });
    }, 5000);
    
    // Clear interval when page is unloaded
    $(window).on('beforeunload', function() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
        }
    });
    {% endif %}
    
    // Update last updated time
    var updateLastUpdated = function() {
        $('#lastUpdated').text(new Date().toLocaleString());
    };
    
    // Update every minute
    setInterval(updateLastUpdated, 60000);
});
</script>
{% endblock %}
