{% extends "base.html" %}

{% block title %}Error {{ error_code }} - KAG Document Processing{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body text-center py-5">
                {% if error_code == 404 %}
                    <i class="fas fa-search fa-4x text-warning mb-4"></i>
                    <h1 class="display-4 text-warning">404</h1>
                    <h4 class="mb-3">Page Not Found</h4>
                    <p class="text-muted mb-4">
                        The page you're looking for doesn't exist or has been moved.
                    </p>
                {% elif error_code == 500 %}
                    <i class="fas fa-exclamation-triangle fa-4x text-danger mb-4"></i>
                    <h1 class="display-4 text-danger">500</h1>
                    <h4 class="mb-3">Internal Server Error</h4>
                    <p class="text-muted mb-4">
                        Something went wrong on our end. Please try again later.
                    </p>
                {% else %}
                    <i class="fas fa-exclamation-circle fa-4x text-warning mb-4"></i>
                    <h1 class="display-4 text-warning">{{ error_code }}</h1>
                    <h4 class="mb-3">Error</h4>
                    <p class="text-muted mb-4">
                        {{ error_message or "An unexpected error occurred." }}
                    </p>
                {% endif %}
                
                <div class="d-grid gap-2 d-md-block">
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>Go to Dashboard
                    </a>
                    <button class="btn btn-outline-secondary" onclick="history.back()">
                        <i class="fas fa-arrow-left me-2"></i>Go Back
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Helpful Links -->
<div class="row justify-content-center mt-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>What can you do?
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="{{ url_for('upload_page') }}" class="text-decoration-none">
                                    <i class="fas fa-upload me-2"></i>Upload a new document
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="{{ url_for('search_page') }}" class="text-decoration-none">
                                    <i class="fas fa-search me-2"></i>Search existing documents
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="{{ url_for('documents_page') }}" class="text-decoration-none">
                                    <i class="fas fa-file-alt me-2"></i>View all documents
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="{{ url_for('graph_page') }}" class="text-decoration-none">
                                    <i class="fas fa-project-diagram me-2"></i>Explore knowledge graph
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
