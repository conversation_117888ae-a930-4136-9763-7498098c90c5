#!/usr/bin/env python3
"""
Comprehensive test script for Mistral AI Knowledge Extraction System.

This script tests:
1. Mistral AI API connectivity and model availability
2. Knowledge extraction functionality
3. Database connectivity and operations
4. End-to-end processing workflow
5. Error handling and edge cases

Usage:
    python test_mistral_knowledge_extraction.py [--debug] [--model MODEL_NAME]
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mistralai import Mistral
from processors.knowledge import KnowledgeGraphGenerator
from db import DatabaseConnection
from core.config import config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'test_mistral_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)

class MistralKnowledgeExtractionTester:
    """Comprehensive tester for Mistral AI knowledge extraction system."""

    def __init__(self, debug: bool = False, model_name: Optional[str] = None):
        """Initialize the tester with configuration."""
        self.debug = debug
        self.model_name = model_name or config.knowledge_creation['entity_extraction']['model']
        self.test_results = {}

        # Load environment variables
        load_dotenv(override=True)

        # Initialize Mistral client
        self.mistral_api_key = os.getenv('MISTRAL_API_KEY')
        if not self.mistral_api_key:
            raise ValueError("MISTRAL_API_KEY not found in environment variables")

        self.mistral_client = Mistral(api_key=self.mistral_api_key)

        # Sample test data
        self.test_texts = [
            {
                "name": "Simple Technology Text",
                "content": """
                Docker is a containerization platform that allows developers to package applications
                and their dependencies into lightweight containers. Docker containers run on Docker Engine,
                which is available for Linux, Windows, and macOS. Docker Hub is a cloud-based registry
                service where developers can store and share container images. Kubernetes is an orchestration
                platform that manages Docker containers at scale.
                """,
                "expected_entities": ["Docker", "Docker Engine", "Docker Hub", "Kubernetes", "Linux", "Windows", "macOS"],
                "expected_relationships": [("Docker containers", "Docker Engine", "runs_on")]
            },
            {
                "name": "Business Context Text",
                "content": """
                Microsoft Corporation is a multinational technology company headquartered in Redmond, Washington.
                The company was founded by Bill Gates and Paul Allen in 1975. Microsoft develops and sells
                computer software, consumer electronics, and personal computers. Azure is Microsoft's cloud
                computing platform that competes with Amazon Web Services (AWS) and Google Cloud Platform.
                """,
                "expected_entities": ["Microsoft Corporation", "Bill Gates", "Paul Allen", "Azure", "Amazon Web Services", "Google Cloud Platform"],
                "expected_relationships": [("Bill Gates", "Microsoft Corporation", "founded"), ("Azure", "Microsoft Corporation", "developed_by")]
            }
        ]

    def test_mistral_api_connectivity(self) -> bool:
        """Test basic Mistral API connectivity and model availability."""
        logger.info("Testing Mistral API connectivity...")

        try:
            # Test with a simple message
            messages = [
                {"role": "user", "content": "Hello, can you respond with just 'OK'?"}
            ]

            response = self.mistral_client.chat.complete(
                model=self.model_name,
                messages=messages,
                max_tokens=10
            )

            if hasattr(response, 'choices') and response.choices:
                content = response.choices[0].message.content.strip()
                logger.info(f"✅ Mistral API connectivity successful. Response: {content}")
                self.test_results['api_connectivity'] = True
                return True
            else:
                logger.error("❌ No response from Mistral API")
                self.test_results['api_connectivity'] = False
                return False

        except Exception as e:
            logger.error(f"❌ Mistral API connectivity failed: {str(e)}")
            self.test_results['api_connectivity'] = False
            return False

    def test_json_response_format(self) -> bool:
        """Test if the model can return properly formatted JSON."""
        logger.info("Testing JSON response format...")

        try:
            messages = [
                {
                    "role": "system",
                    "content": "You must respond with valid JSON only. No other text."
                },
                {
                    "role": "user",
                    "content": 'Return this JSON: {"test": "success", "number": 42}'
                }
            ]

            response = self.mistral_client.chat.complete(
                model=self.model_name,
                messages=messages,
                response_format={"type": "json_object"}
            )

            if hasattr(response, 'choices') and response.choices:
                content = response.choices[0].message.content.strip()

                # Try to parse as JSON
                parsed_json = json.loads(content)
                logger.info(f"✅ JSON format test successful: {parsed_json}")
                self.test_results['json_format'] = True
                return True
            else:
                logger.error("❌ No response for JSON format test")
                self.test_results['json_format'] = False
                return False

        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON format test failed - Invalid JSON: {str(e)}")
            self.test_results['json_format'] = False
            return False
        except Exception as e:
            logger.error(f"❌ JSON format test failed: {str(e)}")
            self.test_results['json_format'] = False
            return False

    def test_database_connectivity(self) -> bool:
        """Test database connectivity and required tables."""
        logger.info("Testing database connectivity...")

        try:
            with DatabaseConnection() as db:
                # Test basic connectivity
                result = db.execute_query("SELECT 1 as test")
                if result and result[0][0] == 1:
                    logger.info("✅ Database connectivity successful")

                    # Check required tables exist
                    tables_to_check = ['Entities', 'Relationships', 'Document_Embeddings']
                    for table in tables_to_check:
                        try:
                            db.execute_query(f"SELECT COUNT(*) FROM {table}")
                            logger.info(f"✅ Table {table} exists and accessible")
                        except Exception as e:
                            logger.warning(f"⚠️  Table {table} issue: {str(e)}")

                    self.test_results['database_connectivity'] = True
                    return True
                else:
                    logger.error("❌ Database connectivity test failed")
                    self.test_results['database_connectivity'] = False
                    return False

        except Exception as e:
            logger.error(f"❌ Database connectivity failed: {str(e)}")
            self.test_results['database_connectivity'] = False
            return False

    def test_knowledge_extraction(self) -> bool:
        """Test knowledge extraction functionality with sample texts."""
        logger.info("Testing knowledge extraction functionality...")

        try:
            generator = KnowledgeGraphGenerator(debug_output=self.debug)
            extraction_results = []

            for i, test_case in enumerate(self.test_texts):
                logger.info(f"Testing with: {test_case['name']}")

                # Extract knowledge
                knowledge = generator.extract_knowledge_sync(test_case['content'])

                # Validate structure
                if not isinstance(knowledge, dict):
                    logger.error(f"❌ Knowledge extraction returned non-dict: {type(knowledge)}")
                    continue

                if 'entities' not in knowledge or 'relationships' not in knowledge:
                    logger.error(f"❌ Missing required keys in knowledge extraction")
                    continue

                entities = knowledge.get('entities', [])
                relationships = knowledge.get('relationships', [])

                logger.info(f"✅ Extracted {len(entities)} entities and {len(relationships)} relationships")

                # Log extracted entities
                if entities:
                    logger.info("Extracted entities:")
                    for entity in entities[:5]:  # Show first 5
                        logger.info(f"  - {entity.get('name', 'Unknown')} ({entity.get('category', 'Unknown')})")

                # Log extracted relationships
                if relationships:
                    logger.info("Extracted relationships:")
                    for rel in relationships[:3]:  # Show first 3
                        logger.info(f"  - {rel.get('source', 'Unknown')} -> {rel.get('target', 'Unknown')} ({rel.get('relation_type', 'Unknown')})")

                extraction_results.append({
                    'test_case': test_case['name'],
                    'entities_count': len(entities),
                    'relationships_count': len(relationships),
                    'entities': entities,
                    'relationships': relationships
                })

                # Save debug output if enabled
                if self.debug:
                    debug_file = f"debug_extraction_{i}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                    with open(debug_file, 'w') as f:
                        json.dump({
                            'test_case': test_case,
                            'extracted_knowledge': knowledge
                        }, f, indent=2)
                    logger.info(f"Debug output saved to {debug_file}")

            if extraction_results:
                logger.info("✅ Knowledge extraction tests completed successfully")
                self.test_results['knowledge_extraction'] = True
                self.test_results['extraction_results'] = extraction_results
                return True
            else:
                logger.error("❌ No successful knowledge extractions")
                self.test_results['knowledge_extraction'] = False
                return False

        except Exception as e:
            logger.error(f"❌ Knowledge extraction test failed: {str(e)}")
            self.test_results['knowledge_extraction'] = False
            return False

    def test_end_to_end_processing(self) -> bool:
        """Test end-to-end processing including database storage."""
        logger.info("Testing end-to-end processing...")

        try:
            generator = KnowledgeGraphGenerator(debug_output=self.debug)

            # Use the first test case
            test_case = self.test_texts[0]
            knowledge = generator.extract_knowledge_sync(test_case['content'])

            if not knowledge or not knowledge.get('entities'):
                logger.error("❌ No knowledge extracted for end-to-end test")
                self.test_results['end_to_end'] = False
                return False

            # Test database storage (but don't actually store to avoid pollution)
            logger.info("Testing database storage logic...")

            with DatabaseConnection() as db:
                # Start a transaction that we'll rollback
                db.execute_query("START TRANSACTION")

                try:
                    # Test entity insertion logic
                    for entity in knowledge['entities'][:2]:  # Test with first 2 entities
                        check_query = "SELECT entity_id FROM Entities WHERE name = %s"
                        existing = db.execute_query(check_query, (entity['name'],))

                        if not existing:
                            insert_query = """
                            INSERT INTO Entities (name, description, aliases, category)
                            VALUES (%s, %s, %s, %s)
                            """
                            db.execute_query(
                                insert_query,
                                (
                                    entity['name'],
                                    entity.get('description', ''),
                                    json.dumps(entity.get('aliases', [])),
                                    entity.get('category', 'CONCEPT')
                                )
                            )
                            logger.info(f"✅ Successfully tested entity insertion: {entity['name']}")

                    # Rollback to avoid actual data insertion
                    db.execute_query("ROLLBACK")
                    logger.info("✅ End-to-end processing test completed (transaction rolled back)")

                    self.test_results['end_to_end'] = True
                    return True

                except Exception as e:
                    db.execute_query("ROLLBACK")
                    raise e

        except Exception as e:
            logger.error(f"❌ End-to-end processing test failed: {str(e)}")
            self.test_results['end_to_end'] = False
            return False

    def test_error_handling(self) -> bool:
        """Test error handling with edge cases."""
        logger.info("Testing error handling...")

        try:
            generator = KnowledgeGraphGenerator(debug_output=self.debug)

            # Test with empty text
            empty_result = generator.extract_knowledge_sync("")
            if isinstance(empty_result, dict) and 'entities' in empty_result:
                logger.info("✅ Empty text handled gracefully")
            else:
                logger.warning("⚠️  Empty text handling could be improved")

            # Test with very short text
            short_result = generator.extract_knowledge_sync("Hello world.")
            if isinstance(short_result, dict) and 'entities' in short_result:
                logger.info("✅ Short text handled gracefully")
            else:
                logger.warning("⚠️  Short text handling could be improved")

            # Test with very long text (truncated)
            long_text = "This is a test sentence. " * 1000
            long_result = generator.extract_knowledge_sync(long_text[:5000])  # Limit to reasonable size
            if isinstance(long_result, dict) and 'entities' in long_result:
                logger.info("✅ Long text handled gracefully")
            else:
                logger.warning("⚠️  Long text handling could be improved")

            self.test_results['error_handling'] = True
            return True

        except Exception as e:
            logger.error(f"❌ Error handling test failed: {str(e)}")
            self.test_results['error_handling'] = False
            return False

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and return comprehensive results."""
        logger.info("=" * 60)
        logger.info("STARTING MISTRAL AI KNOWLEDGE EXTRACTION TESTS")
        logger.info("=" * 60)
        logger.info(f"Model: {self.model_name}")
        logger.info(f"Debug mode: {self.debug}")
        logger.info("")

        # Run tests in order
        tests = [
            ("API Connectivity", self.test_mistral_api_connectivity),
            ("JSON Response Format", self.test_json_response_format),
            ("Database Connectivity", self.test_database_connectivity),
            ("Knowledge Extraction", self.test_knowledge_extraction),
            ("End-to-End Processing", self.test_end_to_end_processing),
            ("Error Handling", self.test_error_handling),
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_name, test_func in tests:
            logger.info(f"Running: {test_name}")
            try:
                if test_func():
                    passed_tests += 1
                    logger.info(f"✅ {test_name} PASSED")
                else:
                    logger.error(f"❌ {test_name} FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name} FAILED with exception: {str(e)}")
            logger.info("")

        # Generate summary
        logger.info("=" * 60)
        logger.info("TEST SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Tests passed: {passed_tests}/{total_tests}")
        logger.info(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")

        if passed_tests == total_tests:
            logger.info("🎉 ALL TESTS PASSED! Your Mistral AI integration is working correctly.")
        elif passed_tests >= total_tests * 0.8:
            logger.info("✅ Most tests passed. Minor issues may need attention.")
        else:
            logger.warning("⚠️  Several tests failed. Please review the issues above.")

        # Add summary to results
        self.test_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': (passed_tests/total_tests)*100,
            'model_used': self.model_name,
            'timestamp': datetime.now().isoformat()
        }

        return self.test_results

    def save_results(self, filename: Optional[str] = None) -> str:
        """Save test results to a JSON file."""
        if not filename:
            filename = f"mistral_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        with open(filename, 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)

        logger.info(f"Test results saved to: {filename}")
        return filename


def main():
    """Main function to run the tests."""
    parser = argparse.ArgumentParser(description="Test Mistral AI Knowledge Extraction System")
    parser.add_argument("--debug", action="store_true", help="Enable debug output")
    parser.add_argument("--model", type=str, help="Specify Mistral model to test")
    parser.add_argument("--save-results", action="store_true", help="Save results to JSON file")

    args = parser.parse_args()

    try:
        # Create tester instance
        tester = MistralKnowledgeExtractionTester(
            debug=args.debug,
            model_name=args.model
        )

        # Run all tests
        results = tester.run_all_tests()

        # Save results if requested
        if args.save_results:
            tester.save_results()

        # Exit with appropriate code
        if results.get('summary', {}).get('passed_tests', 0) == results.get('summary', {}).get('total_tests', 1):
            sys.exit(0)  # All tests passed
        else:
            sys.exit(1)  # Some tests failed

    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
