from fastapi import APIRouter, HTTPException, Depends
from celery.result import AsyncResult
import logging
from api.auth import get_api_key

logger = logging.getLogger(__name__)
router = APIRouter(tags=["tasks"])


@router.get("/task-status/{task_id}", dependencies=[Depends(get_api_key)])
async def get_task_status(task_id: str):
    """Get the status of a Celery task"""
    try:
        task = AsyncResult(task_id)

        # Get task state and info
        state = task.state
        info = {}

        if state == "SUCCESS":
            info = task.result if isinstance(task.result, dict) else {}
        elif state == "FAILURE":
            # Handle database errors specifically
            error = str(task.result)
            if "OperationalError" in error:
                info = {"error": error}
            else:
                info = {"error": str(task.result)}
        else:
            info = task.info if isinstance(task.info, dict) else {}

        # Build response based on state
        response = {
            "task_id": task_id,
            "status": state,
            "message": info.get('status', ''),
            "current": info.get('current', 0),
            "total": info.get('total', 100)
        }

        # Add error info if failed
        if state == "FAILURE":
            response["error"] = info.get("error", str(task.result))

        return response

    except Exception as e:
        logger.error(f"Error getting task status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting task status: {str(e)}"
        )