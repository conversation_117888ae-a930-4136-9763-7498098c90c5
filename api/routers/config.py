from fastapi import APIRouter, HTTPException
import os
import yaml
import logging
from api.models.config import FullConfig

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/config", tags=["config"])

CONFIG_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config', 'config.yaml')


@router.get("", response_model=FullConfig)
async def get_config():
    try:
        logger.info(f"Loading config from {CONFIG_PATH}")
        if not os.path.exists(CONFIG_PATH):
            raise HTTPException(status_code=404, detail=f"Config file not found at {CONFIG_PATH}")

        with open(CONFIG_PATH, 'r') as f:
            try:
                config = yaml.safe_load(f)
                logger.info("YAML loaded successfully")
                logger.info(f"Config structure: {config.keys()}")
            except yaml.YAMLError as e:
                logger.error(f"YAML parsing error: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Error parsing config YAML: {str(e)}")

            try:
                config_model = FullConfig(**config)
                logger.info("Config validated successfully")
                return config_model
            except Exception as e:
                logger.error(f"Config validation error: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Config validation error: {str(e)}")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


@router.post("")
async def update_config(config: FullConfig):
    try:
        logger.info(f"Updating config at {CONFIG_PATH}")
        config_dict = config.model_dump()
        with open(CONFIG_PATH, 'w') as f:
            yaml.safe_dump(config_dict, f, default_flow_style=False)
        logger.info("Config updated successfully")
        return {"status": "success", "message": "Configuration updated successfully"}
    except Exception as e:
        logger.error(f"Error updating config: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error updating config: {str(e)}")