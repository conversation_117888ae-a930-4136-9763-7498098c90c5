from fastapi import APIRouter, HTTPException, Query, File, UploadFile, status, Depends
from pathlib import Path
import logging
from processors.pdf import (
    save_pdf, create_document_record, get_processing_status,
    cleanup_processing, PDFProcessingError
)
from processors.video_processor import (
    VideoProcessingError, SUPPORTED_VIDEO_FORMATS,
    save_video
)
from tasks.pdf_tasks import process_pdf_task
from tasks.video_tasks import process_video_task
from api.auth import get_api_key
from core.models import ProcessingStatusResponse, TaskResponse
from utils.status_cache import get_status_from_cache as get_cached_status, update_status_cache
from db import DatabaseConnection
from api.models.config import DocChunksResponse

logger = logging.getLogger(__name__)
router = APIRouter(tags=["documents"])


@router.get("/processing-status/{doc_id}", response_model=ProcessingStatusResponse)
async def get_status(doc_id: int):
    """Get current processing status"""
    try:
        # Check cache first
        cached_status = get_cached_status(doc_id)
        if cached_status is not None:
            # Check if cached_status is a Pydantic model or a dict
            if hasattr(cached_status, "currentStep"):
                # It's a Pydantic model
                if cached_status.currentStep in ["completed", "failed"]:
                    return cached_status
            elif isinstance(cached_status, dict) and cached_status.get("currentStep") in ["completed", "failed"]:
                # It's a dictionary
                return cached_status

        # Get fresh status from database
        logger.info(f"Getting processing status for doc_id: {doc_id}")
        try:
            status = get_processing_status(doc_id)
            # Update cache with new status
            update_status_cache(doc_id, status)
            return status
        except Exception as e:
            # If database query fails, return last known status from cache if available
            if cached_status is not None:
                return cached_status
            raise

    except ValueError as e:
        logger.error(f"Processing status not found for doc_id {doc_id}: {str(e)}")
        # Return an empty processing status instead of a 404 error
        return ProcessingStatusResponse(currentStep="", fileName="")
    except Exception as e:
        logger.error(f"Error getting processing status: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload-pdf", status_code=status.HTTP_202_ACCEPTED, response_model=TaskResponse, dependencies=[Depends(get_api_key)])
async def upload_pdf(file: UploadFile = File(...)):
    """Upload and process a PDF file"""
    try:
        # Save the uploaded file
        logger.info(f"Saving uploaded file: {file.filename}")
        file_content = await file.read()
        file_path = save_pdf(file_content, file.filename)

        # Create document record
        doc_id = create_document_record(file.filename, str(file_path), len(file_content))
        logger.info(f"Created document record with ID: {doc_id}")

        # Initialize status in cache
        update_status_cache(doc_id, ProcessingStatusResponse(
            currentStep="started",
            fileName=file.filename
        ))

        # Start celery task
        task = process_pdf_task.delay(doc_id)
        logger.info(f"Started Celery task {task.id} for doc_id {doc_id}")

        return TaskResponse(
            task_id=task.id,
            doc_id=doc_id,
            status="pending",
            message="Processing started"
        )

    except PDFProcessingError as e:
        logger.error(f"PDF processing error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error uploading PDF: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload-video", status_code=status.HTTP_202_ACCEPTED, response_model=TaskResponse, dependencies=[Depends(get_api_key)])
async def upload_video(file: UploadFile = File(...)):
    """Upload and process a video file"""
    try:
        # Validate file extension
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in SUPPORTED_VIDEO_FORMATS:
            raise VideoProcessingError(f"Unsupported file format. Supported formats: {', '.join(SUPPORTED_VIDEO_FORMATS)}")

        # Save the uploaded file
        logger.info(f"Saving uploaded file: {file.filename}")
        file_content = await file.read()
        file_path = save_video(file_content, file.filename)

        # Create document record
        doc_id = create_document_record(file.filename, str(file_path), len(file_content))
        logger.info(f"Created document record with ID: {doc_id}")

        # Initialize status in cache
        update_status_cache(doc_id, ProcessingStatusResponse(
            currentStep="started",
            fileName=file.filename
        ))

        # Start celery task
        task = process_video_task.delay(doc_id)
        logger.info(f"Started Celery task {task.id} for doc_id {doc_id}")

        return TaskResponse(
            task_id=task.id,
            doc_id=doc_id,
            status="pending",
            message="Processing started"
        )

    except VideoProcessingError as e:
        logger.error(f"Video processing error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error uploading video: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/cancel-processing/{doc_id}", dependencies=[Depends(get_api_key)])
async def cancel_processing(doc_id: int):
    try:
        cleanup_processing(doc_id)
        return {"status": "cancelled"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/doc-chunks", response_model=DocChunksResponse, dependencies=[Depends(get_api_key)])
async def get_doc_chunks(doc_id: int = Query(..., description="Document ID for which to retrieve chunks")):
    """
    Retrieve document chunks for a given doc_id.
    Returns a list of chunks with content and embedding.
    """
    try:
        with DatabaseConnection() as conn:
            query = "SELECT content FROM Document_Embeddings WHERE doc_id = %s"
            logger.info(f"Executing query for doc_id {doc_id}: {query}")
            rows = conn.execute_query(query, (doc_id,))
            logger.info(f"Query returned {len(rows)} rows")

            chunks = []
            for i, row in enumerate(rows):
                chunks.append({
                    "content": row[0]
                })
                logger.debug(f"Processed chunk {i+1}: content length={len(row[0])}")

            logger.info(f"Successfully processed {len(chunks)} chunks")
            return {"chunks": chunks}
    except Exception as e:
        logger.error(f"Error retrieving document chunks: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve document chunks: {str(e)}")