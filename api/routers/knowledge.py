from fastapi import APIRouter, HTTPException, Depends
import time
import os
from datetime import datetime
from search.engine import RAGQueryEngine
from db import DatabaseConnection
from core.models import (
    SearchRequest, SearchResponse, KBDataResponse, KBStats, DocumentStats,
    GraphResponse, GraphData, GraphNode, GraphLink
)
from api.auth import get_api_key
import logging

logger = logging.getLogger(__name__)
router = APIRouter(tags=["knowledge"])


@router.post("/kag-search", response_model=SearchResponse, dependencies=[Depends(get_api_key)])
async def search_documents(request: SearchRequest):
    """
    Endpoint for document search using natural language queries
    """
    try:
        # Initialize RAG Query Engine and start timing
        start_time = time.time()
        rag_engine = RAGQueryEngine(debug_output=request.debug)

        # Execute query
        response = rag_engine.query(
            query_text=request.query,
            top_k=request.top_k
        )

        # Calculate execution time (override the one in the response for accurate API-level timing)
        execution_time = time.time() - start_time
        response.execution_time = execution_time
        
        # Use the response directly - it already has all the debug info if debug mode was enabled
        return response

    except Exception as e:
        logger.error(f"Search error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


def _get_kb_document_stats(conn, doc_id: int):
    """Get document-specific statistics"""
    doc_stats_query = """
        SELECT
            COUNT(DISTINCT de.embedding_id) as chunk_count,
            COUNT(DISTINCT e.entity_id) as entity_count,
            COUNT(DISTINCT r.relationship_id) as relationship_count
        FROM Document_Embeddings de
        LEFT JOIN Relationships r ON r.doc_id = de.doc_id
        LEFT JOIN (
            SELECT DISTINCT e.entity_id, r.doc_id
            FROM Entities e
            JOIN Relationships r ON r.source_entity_id = e.entity_id OR r.target_entity_id = e.entity_id
        ) e ON e.doc_id = de.doc_id
        WHERE de.doc_id = %s
    """
    return conn.execute_query(doc_stats_query, (doc_id,))[0]


def _create_document_stats(doc_row, doc_detail):
    """Create DocumentStats object from database row and detail"""
    # Get file extension as file_type
    file_type = os.path.splitext(doc_row[1])[1].lstrip('.') if doc_row[1] else 'unknown'
    
    return DocumentStats(
        doc_id=doc_row[0],
        title=doc_row[1],  # Using filename as title
        total_chunks=doc_detail[0],
        total_entities=doc_detail[1],
        total_relationships=doc_detail[2],
        created_at=doc_row[3].isoformat() if doc_row[3] else None,
        file_type=file_type,
        status=doc_row[4]
    )


@router.get("/kbdata", response_model=KBDataResponse, dependencies=[Depends(get_api_key)])
async def get_kb_data():
    """Get knowledge base statistics and document information."""
    start_time = time.time()
    try:
        with DatabaseConnection() as conn:
            # Get total document count and size
            doc_stats_query = """
                SELECT
                    COUNT(*) as doc_count,
                    SUM(file_size) as total_size,
                    SUM(CASE WHEN current_step = 'completed' THEN 1 ELSE 0 END) as processed_count,
                    SUM(CASE WHEN current_step IN ('started', 'chunking', 'embeddings', 'entities', 'relationships') THEN 1 ELSE 0 END) as processing_count,
                    SUM(CASE WHEN current_step = 'failed' THEN 1 ELSE 0 END) as error_count
                FROM ProcessingStatus
            """
            doc_stats = conn.execute_query(doc_stats_query)[0]

            # Get entity stats
            entity_stats_query = """
                SELECT
                    COUNT(*) as total_entities,
                    COUNT(DISTINCT category) as category_count
                FROM Entities
            """
            entity_stats = conn.execute_query(entity_stats_query)[0]

            # Get relationship stats
            rel_stats_query = """
                SELECT
                    COUNT(*) as total_relationships,
                    COUNT(DISTINCT relation_type) as type_count
                FROM Relationships
            """
            rel_stats = conn.execute_query(rel_stats_query)[0]

            # Get recent documents
            recent_docs_query = """
                SELECT
                    p.doc_id,
                    p.file_name as filename,
                    p.file_size,
                    p.created_at as upload_time,
                    p.current_step as status,
                    p.error_message
                FROM ProcessingStatus p
                ORDER BY p.created_at DESC
                LIMIT 10
            """
            recent_docs = conn.execute_query(recent_docs_query)

            documents = []
            for doc in recent_docs:
                doc_id = doc[0]
                doc_detail = _get_kb_document_stats(conn, doc_id)
                documents.append(_create_document_stats(doc, doc_detail))

            # Create KBStats
            kb_stats = KBStats(
                total_documents=doc_stats[0],
                total_chunks=sum(d.total_chunks for d in documents),
                total_entities=entity_stats[0],
                total_relationships=rel_stats[0],
                documents=documents,
                last_updated=datetime.now().isoformat()
            )

            return KBDataResponse(
                stats=kb_stats,
                execution_time=time.time() - start_time
            )

    except Exception as e:
        logger.error(f"Error getting KB stats: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve knowledge base statistics: {str(e)}"
        )


@router.get("/graph-data", response_model=GraphResponse, dependencies=[Depends(get_api_key)])
async def get_graph_data():
    """Get knowledge graph visualization data."""
    start_time = time.time()
    try:
        with DatabaseConnection() as conn:
            # Get unique categories and assign group numbers
            category_query = """
                SELECT DISTINCT category
                FROM Entities
                WHERE category IS NOT NULL
                ORDER BY category
            """
            categories = conn.execute_query(category_query)
            category_to_group = {cat[0]: idx for idx, cat in enumerate(categories, 1)}

            # Get all entities
            entity_query = """
                SELECT
                    entity_id,
                    name,
                    COALESCE(category, 'unknown') as category,
                    COUNT(DISTINCT r1.relationship_id) + COUNT(DISTINCT r2.relationship_id) as connection_count
                FROM Entities e
                LEFT JOIN Relationships r1 ON e.entity_id = r1.source_entity_id
                LEFT JOIN Relationships r2 ON e.entity_id = r2.target_entity_id
                GROUP BY entity_id, name, category
            """
            entities = conn.execute_query(entity_query)

            # Get all relationships
            relationship_query = """
                SELECT
                    r.source_entity_id,
                    r.target_entity_id,
                    r.relation_type,
                    COUNT(*) as weight
                FROM Relationships r
                GROUP BY source_entity_id, target_entity_id, relation_type
            """
            relationships = conn.execute_query(relationship_query)

            # Create nodes
            nodes = [
                GraphNode(
                    id=str(entity[0]),
                    name=entity[1],
                    category=entity[2],
                    group=category_to_group.get(entity[2], 0),
                    val=max(1, int(entity[3]))  # Node size based on connections
                )
                for entity in entities
            ]

            # Create links
            links = [
                GraphLink(
                    source=str(rel[0]),
                    target=str(rel[1]),
                    type=rel[2],
                    value=int(rel[3])  # Link thickness based on relationship count
                )
                for rel in relationships
            ]

            return GraphResponse(
                data=GraphData(nodes=nodes, links=links),
                execution_time=time.time() - start_time
            )

    except Exception as e:
        logger.error(f"Error getting graph data: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving graph data: {str(e)}"
        )