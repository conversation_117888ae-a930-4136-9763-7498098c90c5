from pydantic import BaseModel, Field
from typing import List


class ChunkingConfig(BaseModel):
    semantic_rules: List[str]
    overlap_size: int = Field(ge=0)
    min_chunk_size: int = Field(ge=0)
    max_chunk_size: int = Field(ge=0)


class EntityExtractionConfig(BaseModel):
    model: str
    confidence_threshold: float = Field(ge=0.0, le=1.0)
    min_description_length: int = Field(ge=0)
    max_description_length: int = Field(ge=0)
    description_required: bool
    system_prompt: str
    extraction_prompt_template: str


class SearchConfig(BaseModel):
    top_k: int = Field(ge=1)
    vector_weight: float = Field(ge=0.0, le=1.0)
    text_weight: float = Field(ge=0.0, le=1.0)
    exact_phrase_weight: float = Field(ge=0.0)
    single_term_weight: float = Field(ge=0.0)
    proximity_distance: int = Field(ge=0)
    min_score_threshold: float = Field(ge=0.0, le=1.0)
    min_similarity_score: float = Field(ge=0.0, le=1.0)
    context_window_size: int = Field(ge=0)


class ResponseGenerationConfig(BaseModel):
    temperature: float = Field(ge=0.0, le=1.0)
    max_tokens: int = Field(ge=0)
    citation_style: str
    include_confidence: bool
    prompt_template: str


class RetrievalConfig(BaseModel):
    search: SearchConfig
    response_generation: ResponseGenerationConfig


class KnowledgeCreationConfig(BaseModel):
    chunking: ChunkingConfig
    entity_extraction: EntityExtractionConfig


class FullConfig(BaseModel):
    knowledge_creation: KnowledgeCreationConfig
    retrieval: RetrievalConfig


class DocChunk(BaseModel):
    content: str


class DocChunksResponse(BaseModel):
    chunks: List[DocChunk]