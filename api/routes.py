from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import logging
import os
from datetime import datetime
from db import DatabaseConnection
from dotenv import load_dotenv
from contextlib import asynccontextmanager
from api.routers import config, documents, tasks, knowledge

# Initialize logging
logger = logging.getLogger(__name__)
logger.setLevel(os.getenv('LOG_LEVEL', 'INFO'))

# Load environment variables
load_dotenv(override=True)

# Define constants
DOCUMENTS_DIR = os.path.join(os.getcwd(), "documents")
os.makedirs(DOCUMENTS_DIR, exist_ok=True)


# Database connection
db = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Initialize database connection on startup."""
    try:
        db = DatabaseConnection()
        db.connect()
        logger.info("Database connection established")
    except Exception as e:
        logger.error(f"Failed to initialize database: {str(e)}")
        logger.warning("Application will start without database connection. Database-dependent features may not work.")
        db = None  # Set to None so we can handle it gracefully later

    yield
    """Close database connection on shutdown."""
    if db:
        db.disconnect()
        logger.info("Database connection closed")

app = FastAPI(
    title="KagSearch API",
    description=""",

    API for document processing, search, and knowledge graph functionality.

    Features:
    - PDF document upload and processing
    - Real-time processing status tracking
    - Natural language search
    - Knowledge graph visualization
    - Document analytics

    For detailed documentation, see /docs/api.md
    """,
    version="0.2.1",
    lifespan=lifespan
)

# CORS Configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for testing
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health")
async def health_check():
    """Health check endpoint that tests database connectivity."""
    health_status = {
        "status": "healthy",
        "database": "disconnected",
        "timestamp": datetime.now().isoformat()
    }

    # Test database connection
    try:
        test_db = DatabaseConnection()
        test_db.connect()
        # Check if connection and cursor are not None
        if test_db.conn is not None and test_db.conn.is_connected() and test_db.cursor is not None:
            health_status["database"] = "connected"
            test_db.disconnect()
        else:
            health_status["database"] = "failed"
    except Exception as e:
        health_status["database"] = f"error: {str(e)}"
        health_status["status"] = "degraded"

    return health_status

# Include routers
app.include_router(config.router)
app.include_router(documents.router)
app.include_router(tasks.router)
app.include_router(knowledge.router)
