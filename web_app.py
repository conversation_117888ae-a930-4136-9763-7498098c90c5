#!/usr/bin/env python3
"""
Flask Web Interface for KAG Backend
Provides a user-friendly web interface to interact with the document processing and search API.
"""

import os
import requests
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from werkzeug.utils import secure_filename
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Flask app configuration
app = Flask(__name__)
app.secret_key = os.getenv('FLASK_SECRET_KEY', 'your-secret-key-change-this')
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size

# API Configuration
API_BASE_URL = os.getenv('API_BASE_URL', 'http://localhost:8000')
API_KEY = os.getenv('API_KEY', 'a614192a822e6daef18a68029396879632c768dac57fe826043cf785bcdf519a7')

# Allowed file extensions
ALLOWED_EXTENSIONS = {'pdf', 'mp4', 'avi', 'mov', 'mkv', 'wmv'}

def allowed_file(filename):
    """Check if file extension is allowed."""
    if not filename or '.' not in filename:
        return False
    try:
        ext = filename.rsplit('.', 1)[1].lower()
        return ext in ALLOWED_EXTENSIONS
    except IndexError:
        return False

def make_api_request(endpoint, method='GET', data=None, files=None):
    """Make a request to the API with proper headers."""
    headers = {'X-API-Key': API_KEY}
    url = f"{API_BASE_URL}{endpoint}"
    
    try:
        if method == 'GET':
            response = requests.get(url, headers=headers, params=data)
        elif method == 'POST':
            if files:
                response = requests.post(url, headers=headers, files=files, data=data)
            else:
                headers['Content-Type'] = 'application/json'
                response = requests.post(url, headers=headers, json=data)
        elif method == 'DELETE':
            response = requests.delete(url, headers=headers)
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        logger.error(f"API request failed: {e}")
        return None

@app.route('/')
def index():
    """Main dashboard page."""
    # Get knowledge base statistics
    kb_data = make_api_request('/kbdata')
    
    return render_template('index.html', kb_data=kb_data)

@app.route('/upload')
def upload_page():
    """Document upload page."""
    return render_template('upload.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload."""
    if 'file' not in request.files:
        flash('No file selected', 'error')
        return redirect(request.url)
    
    file = request.files['file']
    if file.filename == '':
        flash('No file selected', 'error')
        return redirect(request.url)
    
    # Log the file being uploaded
    logger.info(f"Attempting to upload file: {file.filename}")
    
    if not allowed_file(file.filename):
        flash(f'Invalid file type. Allowed types: {", ".join(ALLOWED_EXTENSIONS)}', 'error')
        return redirect(request.url)
    
    filename = secure_filename(file.filename)
    
    # Determine the upload endpoint based on file type
    try:
        # Extract file extension safely
        if '.' in filename:
            file_ext = filename.rsplit('.', 1)[1].lower()
            logger.info(f"File extension detected: {file_ext}")
            
            if file_ext == 'pdf':
                endpoint = '/upload-pdf'
                logger.info("Using PDF upload endpoint")
            elif file_ext in {'mp4', 'avi', 'mov', 'mkv', 'wmv'}:
                endpoint = '/upload-video'
                logger.info("Using video upload endpoint")
            else:
                flash(f'Unsupported file format: {file_ext}. Allowed types: {", ".join(ALLOWED_EXTENSIONS)}', 'error')
                return redirect(request.url)
        else:
            logger.error(f"No file extension found in filename: {filename}")
            flash('File must have an extension (.pdf, .mp4, etc.)', 'error')
            return redirect(request.url)
            
        # Prepare file for upload
        files = {'file': (filename, file.stream, file.content_type)}
        
        # Make API request
        logger.info(f"Sending file to API endpoint: {endpoint}")
        result = make_api_request(endpoint, method='POST', files=files)
        
        if result:
            logger.info(f"Upload successful: {result}")
            flash(f'File uploaded successfully! Task ID: {result.get("task_id")}', 'success')
            return redirect(url_for('processing_status', doc_id=result.get('doc_id')))
        else:
            logger.error("API request returned no result")
            flash('Upload failed. Please check server logs for details.', 'error')
    except Exception as e:
        logger.error(f"Error during file upload: {str(e)}")
        flash(f'Error processing file: {str(e)}', 'error')
    
    return redirect(request.url)

@app.route('/search')
def search_page():
    """Search interface page."""
    return render_template('search.html')

@app.route('/search', methods=['POST'])
def search_documents():
    """Handle search requests."""
    query = request.form.get('query', '').strip()
    top_k = int(request.form.get('top_k', 5))
    debug = request.form.get('debug') == 'on'
    show_flow = request.form.get('show_flow') == 'on'  # New option for search flow

    if not query:
        flash('Please enter a search query', 'error')
        return redirect(url_for('search_page'))

    # Make search API request with enhanced debug info
    search_data = {
        'query': query,
        'top_k': top_k,
        'debug': debug or show_flow  # Enable debug if show_flow is requested
    }

    # Track search timing
    import time
    search_start_time = time.time()

    try:
        result = make_api_request('/kag-search', method='POST', data=search_data)
        search_duration = time.time() - search_start_time

        if result:
            # Check if we're in fallback mode (limited search functionality)
            is_limited_mode = False
            generated_response = result.get('generated_response', '')

            if generated_response and 'limited mode' in generated_response.lower():
                is_limited_mode = True
                flash('Search is operating in limited mode due to API service issues. Results may be less accurate.', 'warning')

            # Extract search flow information from the result
            search_flow = extract_search_flow_info(result, query, search_duration)

            return render_template('search_results.html',
                                query=query,
                                results=result.get('results', []),
                                generated_response=generated_response,
                                execution_time=result.get('execution_time', 0),
                                limited_mode=is_limited_mode,
                                debug_info=result.get('debug_info', None),
                                search_flow=search_flow if show_flow else None,
                                show_flow=show_flow)
        else:
            # Generic error when result is None
            flash('Search failed. Please try again later.', 'error')
            return redirect(url_for('search_page'))
    except Exception as e:
        # Log the specific error
        app.logger.error(f"Search error: {str(e)}")

        # Check for specific error types
        error_msg = str(e)
        if '429' in error_msg or 'billing' in error_msg.lower() or 'api key' in error_msg.lower():
            flash('Search service is experiencing issues with the AI provider. Please try again later.', 'warning')
        elif '503' in error_msg or 'service unavailable' in error_msg.lower():
            flash('Search service is temporarily unavailable. Please try again later.', 'error')
        else:
            flash('An error occurred during search. Please try again.', 'error')

        return redirect(url_for('search_page'))

def extract_search_flow_info(result, query, search_duration):
    """Extract and format search flow information for display."""
    flow_info = {
        'query': query,
        'total_duration': search_duration,
        'stages': [],
        'statistics': {},
        'chinese_analysis': None
    }

    # Analyze if this is a Chinese query
    is_chinese_query = any('\u4e00' <= char <= '\u9fff' for char in query)

    # Extract basic statistics
    results = result.get('results', [])
    if results:
        vector_scores = [r.get('vector_score', 0) for r in results]
        text_scores = [r.get('text_score', 0) for r in results]
        combined_scores = [r.get('combined_score', 0) for r in results]

        flow_info['statistics'] = {
            'results_count': len(results),
            'vector_scores': {
                'min': min(vector_scores) if vector_scores else 0,
                'max': max(vector_scores) if vector_scores else 0,
                'avg': sum(vector_scores) / len(vector_scores) if vector_scores else 0
            },
            'text_scores': {
                'min': min(text_scores) if text_scores else 0,
                'max': max(text_scores) if text_scores else 0,
                'avg': sum(text_scores) / len(text_scores) if text_scores else 0
            },
            'combined_scores': {
                'min': min(combined_scores) if combined_scores else 0,
                'max': max(combined_scores) if combined_scores else 0,
                'avg': sum(combined_scores) / len(combined_scores) if combined_scores else 0
            }
        }

        # Analyze entities and relationships
        total_entities = sum(len(r.get('entities', [])) for r in results)
        total_relationships = sum(len(r.get('relationships', [])) for r in results)

        flow_info['statistics']['knowledge_graph'] = {
            'total_entities': total_entities,
            'total_relationships': total_relationships,
            'avg_entities_per_result': total_entities / len(results) if results else 0,
            'avg_relationships_per_result': total_relationships / len(results) if results else 0
        }

    # Add Chinese query analysis if applicable
    if is_chinese_query and results:
        chinese_content_count = 0
        stock_code_count = 0

        for result_item in results:
            content = result_item.get('content', '')
            # Check for Chinese content
            if any('\u4e00' <= char <= '\u9fff' for char in content):
                chinese_content_count += 1
            # Check for stock codes (4-digit numbers)
            import re
            if re.search(r'\b\d{4}\b', content):
                stock_code_count += 1

        flow_info['chinese_analysis'] = {
            'chinese_content_results': chinese_content_count,
            'chinese_content_percentage': (chinese_content_count / len(results)) * 100,
            'stock_code_results': stock_code_count,
            'stock_code_percentage': (stock_code_count / len(results)) * 100,
            'query_translation': detect_query_meaning(query)
        }

    # Create simplified flow stages for display
    flow_info['stages'] = [
        {
            'name': 'Query Processing',
            'description': f'Processed {"Chinese" if is_chinese_query else "English"} query',
            'status': 'completed',
            'details': f'Query length: {len(query)} characters'
        },
        {
            'name': 'Vector Search',
            'description': 'Semantic similarity search using embeddings',
            'status': 'completed' if flow_info['statistics'].get('vector_scores', {}).get('max', 0) > 0 else 'no_results',
            'details': f"Best score: {flow_info['statistics'].get('vector_scores', {}).get('max', 0):.4f}" if flow_info['statistics'] else 'No vector results'
        },
        {
            'name': 'Text Search',
            'description': 'Keyword-based text matching',
            'status': 'completed' if flow_info['statistics'].get('text_scores', {}).get('max', 0) > 0 else 'no_results',
            'details': f"Best score: {flow_info['statistics'].get('text_scores', {}).get('max', 0):.4f}" if flow_info['statistics'] else 'No text results'
        },
        {
            'name': 'Result Merging',
            'description': 'Combined vector and text search results',
            'status': 'completed' if results else 'no_results',
            'details': f"Final results: {len(results)}" if results else 'No results to merge'
        },
        {
            'name': 'Knowledge Graph',
            'description': 'Extracted entities and relationships',
            'status': 'completed' if flow_info['statistics'].get('knowledge_graph', {}).get('total_entities', 0) > 0 else 'no_results',
            'details': f"Entities: {flow_info['statistics'].get('knowledge_graph', {}).get('total_entities', 0)}, Relationships: {flow_info['statistics'].get('knowledge_graph', {}).get('total_relationships', 0)}"
        },
        {
            'name': 'AI Response',
            'description': 'Generated natural language response',
            'status': 'completed' if result.get('generated_response') else 'failed',
            'details': f"Response length: {len(result.get('generated_response', ''))}" if result.get('generated_response') else 'No response generated'
        }
    ]

    return flow_info

def detect_query_meaning(query):
    """Detect the meaning of Chinese queries for display."""
    chinese_terms = {
        '列出': 'List/Show',
        '所有': 'All',
        '庫易': 'Library Easy (Taiwan stock data provider)',
        '股票': 'Stock/Equity',
        '資料': 'Data/Information'
    }

    detected_terms = []
    for chinese, english in chinese_terms.items():
        if chinese in query:
            detected_terms.append(f"{chinese} ({english})")

    if detected_terms:
        return f"Detected terms: {', '.join(detected_terms)}"
    else:
        return "Chinese query detected"

@app.route('/documents')
def documents_page():
    """Document management page."""
    # Get knowledge base data to show documents
    kb_data = make_api_request('/kbdata')
    documents = kb_data.get('stats', {}).get('documents', []) if kb_data else []
    
    return render_template('documents.html', documents=documents)

@app.route('/document/<int:doc_id>')
def document_detail(doc_id):
    """Document detail page showing chunks and metadata."""
    # Get document chunks
    chunks_data = make_api_request(f'/doc-chunks?doc_id={doc_id}')
    chunks = chunks_data.get('chunks', []) if chunks_data else []
    
    # Get processing status
    status_data = make_api_request(f'/processing-status/{doc_id}')
    
    return render_template('document_detail.html', 
                         doc_id=doc_id, 
                         chunks=chunks, 
                         status=status_data)

@app.route('/status/<int:doc_id>')
def processing_status(doc_id):
    """Processing status page."""
    status_data = make_api_request(f'/processing-status/{doc_id}')
    
    # Format current time for the template
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    return render_template('processing_status.html', 
                         doc_id=doc_id, 
                         status=status_data,
                         current_time=current_time)

@app.route('/graph')
def graph_page():
    """Knowledge graph visualization page."""
    return render_template('graph.html')

@app.route('/api/graph-data')
def api_graph_data():
    """Proxy endpoint for graph data."""
    graph_data = make_api_request('/graph-data')
    return jsonify(graph_data) if graph_data else jsonify({'error': 'Failed to fetch graph data'})

@app.route('/api/status/<int:doc_id>')
def api_status(doc_id):
    """API endpoint for real-time status updates."""
    status_data = make_api_request(f'/processing-status/{doc_id}')
    return jsonify(status_data) if status_data else jsonify({'error': 'Failed to fetch status'})

@app.route('/cancel/<int:doc_id>', methods=['POST'])
def cancel_processing(doc_id):
    """Cancel document processing."""
    result = make_api_request(f'/cancel-processing/{doc_id}', method='DELETE')
    
    if result:
        flash('Processing cancelled successfully', 'success')
    else:
        flash('Failed to cancel processing', 'error')
    
    return redirect(url_for('documents_page'))

@app.errorhandler(404)
def not_found_error(error):
    return render_template('error.html', error_code=404, error_message="Page not found"), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('error.html', error_code=500, error_message="Internal server error"), 500

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    
    # Run the Flask app
    app.run(
        host=os.getenv('FLASK_HOST', '0.0.0.0'),
        port=int(os.getenv('FLASK_PORT', 5000)),
        debug=os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    )
