#!/usr/bin/env python3
"""
Redis Setup Helper Script

This script helps set up Redis for the PDF processing API.
It provides multiple options for getting Redis running.
"""

import os
import sys
import subprocess
import socket
import time
import logging
import platform

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def check_redis_running(host='localhost', port=6379, timeout=5):
    """Check if Redis is running."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception:
        return False


def check_command_available(command):
    """Check if a command is available in PATH."""
    try:
        subprocess.run([command, '--version'], 
                      capture_output=True, check=False)
        return True
    except FileNotFoundError:
        return False


def install_redis_macos():
    """Install Redis on macOS using Homebrew."""
    logger.info("Installing Redis on macOS using Homebrew...")
    
    if not check_command_available('brew'):
        logger.error("Homebrew not found. Please install Homebrew first:")
        logger.error("  /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
        return False
    
    try:
        # Install Redis
        logger.info("Running: brew install redis")
        result = subprocess.run(['brew', 'install', 'redis'], 
                              capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Failed to install Redis: {result.stderr}")
            return False
        
        logger.info("✓ Redis installed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error installing Redis: {e}")
        return False


def start_redis_macos():
    """Start Redis on macOS using Homebrew services."""
    logger.info("Starting Redis on macOS...")
    
    try:
        # Start Redis service
        logger.info("Running: brew services start redis")
        result = subprocess.run(['brew', 'services', 'start', 'redis'], 
                              capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.warning(f"brew services failed: {result.stderr}")
            # Try starting Redis directly
            logger.info("Trying to start Redis directly...")
            subprocess.Popen(['redis-server'], 
                           stdout=subprocess.DEVNULL, 
                           stderr=subprocess.DEVNULL)
        
        # Wait a moment and check if Redis is running
        time.sleep(2)
        if check_redis_running():
            logger.info("✓ Redis is now running")
            return True
        else:
            logger.error("✗ Redis failed to start")
            return False
            
    except Exception as e:
        logger.error(f"Error starting Redis: {e}")
        return False


def install_redis_ubuntu():
    """Install Redis on Ubuntu/Debian."""
    logger.info("Installing Redis on Ubuntu/Debian...")
    
    try:
        # Update package list
        logger.info("Updating package list...")
        subprocess.run(['sudo', 'apt-get', 'update'], check=True)
        
        # Install Redis
        logger.info("Installing redis-server...")
        subprocess.run(['sudo', 'apt-get', 'install', '-y', 'redis-server'], 
                      check=True)
        
        logger.info("✓ Redis installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install Redis: {e}")
        return False
    except Exception as e:
        logger.error(f"Error installing Redis: {e}")
        return False


def start_redis_ubuntu():
    """Start Redis on Ubuntu/Debian using systemctl."""
    logger.info("Starting Redis on Ubuntu/Debian...")
    
    try:
        # Start Redis service
        logger.info("Running: sudo systemctl start redis-server")
        subprocess.run(['sudo', 'systemctl', 'start', 'redis-server'], 
                      check=True)
        
        # Enable Redis to start on boot
        logger.info("Enabling Redis to start on boot...")
        subprocess.run(['sudo', 'systemctl', 'enable', 'redis-server'], 
                      check=True)
        
        # Wait a moment and check if Redis is running
        time.sleep(2)
        if check_redis_running():
            logger.info("✓ Redis is now running")
            return True
        else:
            logger.error("✗ Redis failed to start")
            return False
            
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to start Redis: {e}")
        return False
    except Exception as e:
        logger.error(f"Error starting Redis: {e}")
        return False


def start_redis_docker():
    """Start Redis using Docker."""
    logger.info("Starting Redis using Docker...")
    
    if not check_command_available('docker'):
        logger.error("Docker not found. Please install Docker first.")
        return False
    
    try:
        # Check if Redis container is already running
        result = subprocess.run(['docker', 'ps', '--filter', 'name=redis-test', 
                               '--format', '{{.Names}}'], 
                              capture_output=True, text=True)
        
        if 'redis-test' in result.stdout:
            logger.info("Redis container already running")
            return True
        
        # Start Redis container
        logger.info("Starting Redis container...")
        subprocess.run([
            'docker', 'run', '-d', 
            '--name', 'redis-test',
            '-p', '6379:6379',
            'redis:alpine'
        ], check=True)
        
        # Wait a moment and check if Redis is running
        time.sleep(3)
        if check_redis_running():
            logger.info("✓ Redis is now running in Docker")
            logger.info("To stop: docker stop redis-test")
            logger.info("To remove: docker rm redis-test")
            return True
        else:
            logger.error("✗ Redis container failed to start properly")
            return False
            
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to start Redis container: {e}")
        return False
    except Exception as e:
        logger.error(f"Error starting Redis with Docker: {e}")
        return False


def setup_redis_auto():
    """Automatically set up Redis based on the operating system."""
    logger.info("Setting up Redis automatically...")
    
    # Check if Redis is already running
    if check_redis_running():
        logger.info("✓ Redis is already running")
        return True
    
    system = platform.system().lower()
    
    if system == 'darwin':  # macOS
        logger.info("Detected macOS")
        if check_command_available('brew'):
            if not check_command_available('redis-server'):
                if not install_redis_macos():
                    return False
            return start_redis_macos()
        else:
            logger.warning("Homebrew not available, trying Docker...")
            return start_redis_docker()
    
    elif system == 'linux':
        logger.info("Detected Linux")
        # Try to detect if it's Ubuntu/Debian
        if os.path.exists('/etc/debian_version'):
            logger.info("Detected Debian/Ubuntu")
            if not check_command_available('redis-server'):
                if not install_redis_ubuntu():
                    return False
            return start_redis_ubuntu()
        else:
            logger.warning("Unknown Linux distribution, trying Docker...")
            return start_redis_docker()
    
    else:
        logger.warning(f"Unknown operating system: {system}")
        logger.info("Trying Docker as fallback...")
        return start_redis_docker()


def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Set up Redis for PDF processing API")
    parser.add_argument("--check-only", action="store_true", 
                       help="Only check if Redis is running")
    parser.add_argument("--docker", action="store_true", 
                       help="Use Docker to run Redis")
    parser.add_argument("--manual", action="store_true", 
                       help="Show manual setup instructions")
    
    args = parser.parse_args()
    
    logger.info("=" * 60)
    logger.info("Redis Setup Helper")
    logger.info("=" * 60)
    
    # Check current status
    if check_redis_running():
        logger.info("✓ Redis is already running on localhost:6379")
        if args.check_only:
            sys.exit(0)
        else:
            logger.info("No setup needed!")
            sys.exit(0)
    else:
        logger.info("✗ Redis is not running on localhost:6379")
    
    if args.check_only:
        logger.info("Use this script without --check-only to set up Redis")
        sys.exit(1)
    
    if args.manual:
        logger.info("Manual Redis setup instructions:")
        logger.info("")
        logger.info("macOS (with Homebrew):")
        logger.info("  brew install redis")
        logger.info("  brew services start redis")
        logger.info("")
        logger.info("Ubuntu/Debian:")
        logger.info("  sudo apt-get update")
        logger.info("  sudo apt-get install redis-server")
        logger.info("  sudo systemctl start redis-server")
        logger.info("")
        logger.info("Docker:")
        logger.info("  docker run -d -p 6379:6379 --name redis-test redis:alpine")
        logger.info("")
        logger.info("Windows:")
        logger.info("  Download Redis from: https://github.com/microsoftarchive/redis/releases")
        logger.info("  Or use Docker Desktop")
        sys.exit(0)
    
    # Set up Redis
    if args.docker:
        success = start_redis_docker()
    else:
        success = setup_redis_auto()
    
    if success:
        logger.info("=" * 60)
        logger.info("✓ Redis setup completed successfully!")
        logger.info("You can now run the PDF processing API tests.")
        logger.info("=" * 60)
        sys.exit(0)
    else:
        logger.error("=" * 60)
        logger.error("✗ Redis setup failed!")
        logger.error("Please try manual setup or use Docker:")
        logger.error("  python3 setup_redis.py --docker")
        logger.error("  python3 setup_redis.py --manual")
        logger.error("=" * 60)
        sys.exit(1)


if __name__ == "__main__":
    main()
