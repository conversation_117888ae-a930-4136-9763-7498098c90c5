# Docker Redis Setup Guide

This guide provides multiple ways to set up Redis using Docker for the PDF processing API.

## 📁 Files Overview

### Docker Compose Files
- **`docker-compose.redis-only.yml`** - Redis only (lightweight)
- **`docker-compose.dev.yml`** - Redis + monitoring tools (development)
- **`docker-compose.yml`** - Full application stack (existing)

### Configuration Files
- **`redis.conf`** - Redis configuration for development
- **`setup_docker_redis.py`** - Automated Docker setup script

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)
```bash
# Start Redis only
python3 setup_docker_redis.py --mode redis-only

# Start development environment with monitoring
python3 setup_docker_redis.py --mode dev

# Start full application stack
python3 setup_docker_redis.py --mode full

# Check status
python3 setup_docker_redis.py --status

# Stop services
python3 setup_docker_redis.py --stop
```

### Option 2: Manual Docker Compose
```bash
# Redis only
docker-compose -f docker-compose.redis-only.yml up -d

# Development environment
docker-compose -f docker-compose.dev.yml up -d

# Full stack
docker-compose up -d

# Stop services
docker-compose -f docker-compose.redis-only.yml down
```

### Option 3: Simple Docker Command
```bash
# Quick Redis container
docker run -d --name redis-test -p 6379:6379 redis:alpine

# Stop and remove
docker stop redis-test && docker rm redis-test
```

## 📋 Setup Options

### 1. Redis Only (`redis-only`)
**Use case**: Just need Redis for testing API

**What it includes**:
- Redis 7 (Alpine Linux)
- Port 6379 exposed
- Persistent data volume
- Health checks
- Memory optimization

**Start**:
```bash
python3 setup_docker_redis.py --mode redis-only
# or
docker-compose -f docker-compose.redis-only.yml up -d
```

**Access**:
- Redis: `localhost:6379`

### 2. Development Environment (`dev`)
**Use case**: Development with Redis monitoring

**What it includes**:
- Redis 7 with custom configuration
- Redis Commander (Web UI)
- RedisInsight (Advanced monitoring)
- Persistent data volumes

**Start**:
```bash
python3 setup_docker_redis.py --mode dev
# or
docker-compose -f docker-compose.dev.yml up -d
```

**Access**:
- Redis: `localhost:6379`
- Redis Commander: `http://localhost:8081` (admin/admin)
- RedisInsight: `http://localhost:8001`

### 3. Full Application Stack (`full`)
**Use case**: Complete application with all services

**What it includes**:
- Redis
- Backend API
- Celery worker
- All environment variables from `.env`

**Start**:
```bash
python3 setup_docker_redis.py --mode full
# or
docker-compose up -d
```

**Access**:
- API: `http://localhost:8000`
- Redis: `localhost:6379`

## 🔧 Configuration

### Environment Variables
The setup uses these Redis-related environment variables:
```bash
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

### Redis Configuration
The `redis.conf` file includes optimizations for development:
- Memory limit: 512MB
- Persistence: RDB + AOF
- Eviction policy: allkeys-lru
- Slow log monitoring
- Threaded I/O

### Docker Networks
All services use the `kag-network` bridge network for internal communication.

## 📊 Monitoring & Management

### Redis Commander
Web-based Redis management interface:
- URL: `http://localhost:8081`
- Username: `admin`
- Password: `admin`
- Features: Key browsing, CLI, monitoring

### RedisInsight
Advanced Redis monitoring tool:
- URL: `http://localhost:8001`
- Features: Performance monitoring, memory analysis, profiling

### Command Line Monitoring
```bash
# Connect to Redis CLI
docker exec -it kag-redis redis-cli

# Monitor Redis commands
docker exec -it kag-redis redis-cli monitor

# Check Redis info
docker exec -it kag-redis redis-cli info

# View slow log
docker exec -it kag-redis redis-cli slowlog get 10
```

## 🛠 Troubleshooting

### Common Issues

#### 1. Port Already in Use
```
Error: bind: address already in use
```
**Solution**: Stop existing Redis or use different port:
```bash
# Check what's using port 6379
lsof -i :6379

# Stop existing Redis
brew services stop redis  # macOS
sudo systemctl stop redis-server  # Ubuntu

# Or use different port in docker-compose
ports:
  - "6380:6379"
```

#### 2. Docker Not Running
```
Error: Cannot connect to the Docker daemon
```
**Solution**: Start Docker Desktop or Docker daemon

#### 3. Permission Issues
```
Error: permission denied while trying to connect to Docker daemon
```
**Solution**: Add user to docker group:
```bash
sudo usermod -aG docker $USER
# Then logout and login again
```

#### 4. Memory Issues
```
Redis is running out of memory
```
**Solution**: Increase memory limit in `redis.conf`:
```
maxmemory 1gb
```

### Health Checks
```bash
# Check if Redis is responding
python3 setup_docker_redis.py --check-only

# Manual health check
docker exec kag-redis redis-cli ping

# Check container health
docker ps --filter name=kag-redis
```

### Logs
```bash
# View Redis logs
docker logs kag-redis

# Follow logs in real-time
docker logs -f kag-redis

# View all container logs
docker-compose -f docker-compose.dev.yml logs
```

## 🔄 Data Persistence

### Volume Management
```bash
# List volumes
docker volume ls | grep redis

# Backup Redis data
docker run --rm -v kag_redis_data:/data -v $(pwd):/backup alpine tar czf /backup/redis-backup.tar.gz -C /data .

# Restore Redis data
docker run --rm -v kag_redis_data:/data -v $(pwd):/backup alpine tar xzf /backup/redis-backup.tar.gz -C /data

# Remove volumes (WARNING: deletes data)
docker volume rm kag_redis_data
```

### Data Location
- **Redis data**: Stored in Docker volume `redis_data`
- **RedisInsight data**: Stored in Docker volume `redisinsight_data`
- **Configuration**: Mounted from `./redis.conf`

## 🚀 Performance Tuning

### Memory Optimization
```bash
# Check memory usage
docker exec kag-redis redis-cli info memory

# Set memory policy
docker exec kag-redis redis-cli config set maxmemory-policy allkeys-lru

# Enable memory sampling
docker exec kag-redis redis-cli config set maxmemory-samples 10
```

### Connection Tuning
```bash
# Check connected clients
docker exec kag-redis redis-cli info clients

# Set max connections
docker exec kag-redis redis-cli config set maxclients 1000
```

## 📚 Additional Commands

### Useful Docker Commands
```bash
# View container stats
docker stats kag-redis

# Execute commands in container
docker exec -it kag-redis sh

# Copy files to/from container
docker cp redis.conf kag-redis:/usr/local/etc/redis/

# Restart container
docker restart kag-redis

# View container details
docker inspect kag-redis
```

### Redis Commands
```bash
# Connect to Redis
redis-cli -h localhost -p 6379

# Test connection
redis-cli ping

# Monitor all commands
redis-cli monitor

# Get server info
redis-cli info

# List all keys
redis-cli keys "*"

# Flush all data (WARNING: deletes everything)
redis-cli flushall
```

## 🔒 Security Notes

### Development vs Production
- The provided configurations are for **development only**
- For production, enable authentication:
  ```
  requirepass your_secure_password
  ```
- Use TLS encryption for production
- Restrict network access with firewall rules

### Default Credentials
- Redis Commander: `admin/admin`
- RedisInsight: No authentication (development only)

## 📝 Next Steps

After Redis is running:
1. **Test the connection**: `python3 setup_docker_redis.py --check-only`
2. **Run API tests**: `python3 test_api_python.py`
3. **Start the API server**: `python3 main.py`
4. **Upload a PDF**: Use the test scripts or curl commands

## 🤝 Contributing

When modifying Docker configurations:
1. Test all three modes (redis-only, dev, full)
2. Update this documentation
3. Verify health checks work properly
4. Test data persistence across restarts
