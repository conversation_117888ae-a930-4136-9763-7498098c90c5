#!/usr/bin/env python3
"""
Simple script to check the current database schema for Document_Embeddings table.
"""

import os
import sys
from dotenv import load_dotenv
from db import DatabaseConnection

# Load environment variables
load_dotenv()

def check_schema():
    """Check the current schema of Document_Embeddings table."""
    print("🔍 Checking Document_Embeddings table schema...")
    
    conn = DatabaseConnection()
    try:
        conn.connect()
        print("✅ Connected to database")
        
        # Check the table structure
        print("\n📋 Table structure:")
        result = conn.execute_query("DESCRIBE Document_Embeddings")
        if result:
            print("Column Name | Type | Null | Key | Default | Extra")
            print("-" * 60)
            for row in result:
                print(f"{row[0]:<12} | {row[1]:<20} | {row[2]:<4} | {row[3]:<3} | {row[4] or 'NULL':<7} | {row[5] or ''}")
        else:
            print("❌ No result from DESCRIBE query")
        
        # Check indexes
        print("\n📊 Table indexes:")
        result = conn.execute_query("SHOW INDEXES FROM Document_Embeddings")
        if result:
            print("Table | Non_unique | Key_name | Seq_in_index | Column_name | Collation")
            print("-" * 70)
            for row in result:
                print(f"{row[0]:<15} | {row[1]:<10} | {row[2]:<15} | {row[3]:<12} | {row[4]:<11} | {row[5] or ''}")
        else:
            print("❌ No result from SHOW INDEXES query")
        
        # Check if there's any data
        print("\n📈 Row count:")
        result = conn.execute_query("SELECT COUNT(*) FROM Document_Embeddings")
        if result:
            count = result[0][0]
            print(f"Total rows: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking schema: {e}")
        return False
    finally:
        conn.disconnect()

if __name__ == "__main__":
    check_schema()
