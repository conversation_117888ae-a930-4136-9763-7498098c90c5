#!/usr/bin/env python3
"""
Migration script to update database schema for Mistral AI embeddings.
This script changes the embedding dimension from 1536 (OpenAI) to 1024 (Mistral AI).
"""

import os
import sys
from dotenv import load_dotenv
from db import DatabaseConnection

# Load environment variables
load_dotenv()

def run_migration():
    """Run the database migration for Mistral AI embeddings."""
    print("🔄 Starting migration to Mistral AI embeddings...")

    # Read the migration SQL file
    migration_file = "migrate_to_mistral_embeddings.sql"
    if not os.path.exists(migration_file):
        print(f"❌ Migration file {migration_file} not found!")
        return False

    with open(migration_file, 'r') as f:
        migration_sql = f.read()

    # Split the SQL into individual statements
    statements = [stmt.strip() for stmt in migration_sql.split(';') if stmt.strip() and not stmt.strip().startswith('--')]

    # Try to connect to database with fallback for local execution
    conn = DatabaseConnection()
    try:
        # First try the default connection
        try:
            conn.connect()
            print("✅ Connected to database")
        except Exception as e:
            print(f"⚠️  Default connection failed: {e}")
            print("🔄 Trying alternative connection...")

            # Try connecting to localhost instead of host.docker.internal
            original_host = os.getenv('SINGLESTORE_HOST')
            os.environ['SINGLESTORE_HOST'] = 'localhost'
            conn = DatabaseConnection()
            conn.connect()
            print("✅ Connected to database (localhost)")
            # Restore original host
            if original_host:
                os.environ['SINGLESTORE_HOST'] = original_host

        # Execute each statement
        for i, statement in enumerate(statements, 1):
            if statement.upper().startswith(('DROP', 'ALTER', 'DELETE', 'DESCRIBE', 'SHOW')):
                try:
                    print(f"📝 Executing statement {i}: {statement[:50]}...")
                    result = conn.execute_query(statement)
                    if result and statement.upper().startswith(('DESCRIBE', 'SHOW')):
                        print(f"   Result: {result}")
                    print(f"✅ Statement {i} completed successfully")
                except Exception as e:
                    if "doesn't exist" in str(e) or "Unknown column" in str(e):
                        print(f"⚠️  Statement {i} skipped (already applied): {e}")
                    else:
                        print(f"❌ Statement {i} failed: {e}")
                        # Continue with other statements
            else:
                print(f"⏭️  Skipping statement {i}: {statement[:50]}...")

        print("\n🎉 Migration completed successfully!")
        print("📊 Database schema has been updated for Mistral AI embeddings (1024 dimensions)")
        return True

    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False
    finally:
        conn.disconnect()

def verify_migration():
    """Verify that the migration was successful."""
    print("\n🔍 Verifying migration...")

    conn = DatabaseConnection()
    try:
        conn.connect()

        # Check the embedding column definition
        result = conn.execute_query("DESCRIBE Document_Embeddings")
        for row in result:
            if row[0] == 'embedding':  # column name
                column_type = row[1]  # column type
                print(f"✅ Embedding column type: {column_type}")
                if 'vector(1024)' in column_type.lower():
                    print("✅ Migration verified: Embedding dimension is now 1024 (Mistral AI)")
                    return True
                else:
                    print(f"❌ Migration verification failed: Expected VECTOR(1024), got {column_type}")
                    return False

        print("❌ Embedding column not found in Document_Embeddings table")
        return False

    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False
    finally:
        conn.disconnect()

def main():
    """Main function to run the migration and verification."""
    print("🚀 Mistral AI Embedding Migration Tool")
    print("=" * 50)

    # Run the migration
    if not run_migration():
        print("❌ Migration failed. Exiting.")
        sys.exit(1)

    # Verify the migration
    if not verify_migration():
        print("❌ Migration verification failed. Please check the database manually.")
        sys.exit(1)

    print("\n🎉 All done! Your database is now ready for Mistral AI embeddings.")
    print("💡 You can now restart your application and process documents with Mistral AI.")

if __name__ == "__main__":
    main()
