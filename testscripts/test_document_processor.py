#!/usr/bin/env python3
"""
Test script for DocumentProcessor class from main.py

This script provides comprehensive testing for the DocumentProcessor class,
including unit tests with mocked dependencies and integration tests with sample data.

Usage:
    python test_document_processor.py [--unit-only] [--integration-only] [--verbose]
"""

import os
import sys
import json
import tempfile
import unittest
from unittest.mock import Mock, patch, MagicMock, mock_open
import logging
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Mock problematic imports before importing main
with patch.dict('sys.modules', {
    'mysql.connector': Mock(),
    'mysql': Mock(),
    'db': Mock(),
    'db.connection': Mock(),
    'celery_app': Mock(),
    'api.routes': Mock(),
}):
    # Import the DocumentProcessor class
    from main import DocumentProcessor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class TestDocumentProcessor(unittest.TestCase):
    """Unit tests for DocumentProcessor class with mocked dependencies."""

    def setUp(self):
        """Set up test fixtures before each test method."""
        # Mock environment variables
        self.env_patcher = patch.dict(os.environ, {
            'GEMINI_API_KEY': 'test_gemini_key_12345678',
            'OPENAI_API_KEY': 'test_openai_key_12345678',
            'PROJECT_ID': 'test_project_id',
            'EMBEDDING_MODEL': 'text-embedding-ada-002'
        })
        self.env_patcher.start()

        # Mock external dependencies
        self.genai_patcher = patch('main.genai')
        self.openai_patcher = patch('main.OpenAIClient')
        self.db_patcher = patch('main.DatabaseConnection')

        self.mock_genai = self.genai_patcher.start()
        self.mock_openai_client = self.openai_patcher.start()
        self.mock_db = self.db_patcher.start()

        # Configure mocks
        self.mock_openai_instance = Mock()
        self.mock_openai_client.return_value = self.mock_openai_instance

        self.mock_gemini_model = Mock()
        self.mock_genai.GenerativeModel.return_value = self.mock_gemini_model

    def tearDown(self):
        """Clean up after each test method."""
        self.env_patcher.stop()
        self.genai_patcher.stop()
        self.openai_patcher.stop()
        self.db_patcher.stop()

    def test_init_success(self):
        """Test successful initialization of DocumentProcessor."""
        processor = DocumentProcessor()

        # Verify API configurations
        self.mock_genai.configure.assert_called_once_with(api_key='test_gemini_key_12345678')
        self.mock_openai_client.assert_called_once_with(api_key='test_openai_key_12345678')
        self.mock_genai.GenerativeModel.assert_called_once_with('gemini-2.0-flash')

        # Verify attributes
        self.assertEqual(processor.gemini_api_key, 'test_gemini_key_12345678')
        self.assertEqual(processor.openai_api_key, 'test_openai_key_12345678')
        self.assertEqual(processor.project_id, 'test_project_id')
        self.assertEqual(processor.embedding_model, 'text-embedding-ada-002')

    def test_init_missing_env_vars(self):
        """Test initialization failure with missing environment variables."""
        with patch.dict(os.environ, {}, clear=True):
            with self.assertRaises(ValueError) as context:
                DocumentProcessor()
            self.assertIn("API keys or PROJECT_ID are missing", str(context.exception))

    @patch('builtins.open', new_callable=mock_open)
    @patch('main.PdfReader')
    def test_get_chunks_success(self, mock_pdf_reader, mock_file):
        """Test successful PDF chunk processing."""
        # Setup mocks
        mock_page = Mock()
        mock_page.extract_text.return_value = "Sample PDF text content"
        mock_reader = Mock()
        mock_reader.pages = [mock_page]
        mock_pdf_reader.return_value = mock_reader

        mock_response = Mock()
        mock_response.text = "<chunk>Sample chunk 1</chunk>\n<chunk>Sample chunk 2</chunk>"
        self.mock_gemini_model.generate_content.return_value = mock_response

        processor = DocumentProcessor()

        # Test the method
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_pdf:
            result_path = processor.get_chunks(temp_pdf.name)

            # Verify calls
            mock_pdf_reader.assert_called_once_with(temp_pdf.name)
            self.mock_gemini_model.generate_content.assert_called_once()

            # Verify result
            expected_md_path = temp_pdf.name.replace('.pdf', '.md')
            self.assertEqual(result_path, expected_md_path)

            # Clean up
            os.unlink(temp_pdf.name)

    def test_create_embeddings_success(self):
        """Test successful embedding creation from markdown chunks."""
        # Setup test data
        markdown_content = """
        Some intro text
        <chunk>First chunk content with meaningful text</chunk>
        Some middle text
        <chunk>Second chunk content with more text</chunk>
        Some end text
        """

        # Mock OpenAI response
        mock_embedding_response = Mock()
        mock_embedding_data = Mock()
        mock_embedding_data.embedding = [0.1] * 1536  # 1536 dimensions
        mock_embedding_response.data = [mock_embedding_data]
        self.mock_openai_instance.Embeddings.create.return_value = mock_embedding_response

        processor = DocumentProcessor()

        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_md:
            temp_md.write(markdown_content)
            temp_md.flush()

            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_json:
                try:
                    processor.create_embeddings(temp_md.name, temp_json.name)

                    # Verify OpenAI was called for each chunk
                    self.assertEqual(self.mock_openai_instance.Embeddings.create.call_count, 2)

                    # Verify output file exists and has correct structure
                    self.assertTrue(os.path.exists(temp_json.name))

                    with open(temp_json.name, 'r') as f:
                        result_data = json.load(f)

                    self.assertEqual(len(result_data), 2)
                    self.assertIn('chunk_index', result_data[0])
                    self.assertIn('text', result_data[0])
                    self.assertIn('embedding', result_data[0])
                    self.assertEqual(len(result_data[0]['embedding']), 1536)

                finally:
                    os.unlink(temp_md.name)
                    os.unlink(temp_json.name)

    def test_create_embeddings_no_chunks(self):
        """Test embedding creation when no chunks are found."""
        markdown_content = "This is plain markdown without chunk tags."

        # Mock OpenAI response
        mock_embedding_response = Mock()
        mock_embedding_data = Mock()
        mock_embedding_data.embedding = [0.1] * 1536
        mock_embedding_response.data = [mock_embedding_data]
        self.mock_openai_instance.Embeddings.create.return_value = mock_embedding_response

        processor = DocumentProcessor()

        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_md:
            temp_md.write(markdown_content)
            temp_md.flush()

            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_json:
                try:
                    processor.create_embeddings(temp_md.name, temp_json.name)

                    # Should process the whole text as a single chunk
                    self.assertEqual(self.mock_openai_instance.Embeddings.create.call_count, 1)

                finally:
                    os.unlink(temp_md.name)
                    os.unlink(temp_json.name)

    def test_insert_embeddings_to_db(self):
        """Test database insertion of embeddings."""
        # Create test embeddings data
        test_embeddings = [
            {
                "chunk_index": 0,
                "text": "Test chunk 1",
                "embedding": [0.1] * 1536
            },
            {
                "chunk_index": 1,
                "text": "Test chunk 2",
                "embedding": [0.2] * 1536
            }
        ]

        # Mock database connection
        mock_db_instance = Mock()
        self.mock_db.return_value.__enter__.return_value = mock_db_instance

        processor = DocumentProcessor()

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_json:
            json.dump(test_embeddings, temp_json)
            temp_json.flush()

            try:
                processor.insert_embeddings_to_db(temp_json.name, document_id=456)

                # Verify database operations
                self.assertTrue(mock_db_instance.execute_query.called)
                call_args_list = mock_db_instance.execute_query.call_args_list

                # Should have document insert + 2 embedding inserts
                self.assertEqual(len(call_args_list), 3)

            finally:
                os.unlink(temp_json.name)

    @patch('main.PdfReader')
    def test_get_chunks_pdf_error(self, mock_pdf_reader):
        """Test error handling in PDF processing."""
        mock_pdf_reader.side_effect = Exception("PDF read error")

        processor = DocumentProcessor()

        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_pdf:
            try:
                with self.assertRaises(Exception) as context:
                    processor.get_chunks(temp_pdf.name)
                self.assertIn("PDF read error", str(context.exception))
            finally:
                os.unlink(temp_pdf.name)


class IntegrationTestDocumentProcessor(unittest.TestCase):
    """Integration tests for DocumentProcessor with real file operations."""

    def setUp(self):
        """Set up integration test fixtures."""
        # Only mock the external APIs, not file operations
        self.env_patcher = patch.dict(os.environ, {
            'GEMINI_API_KEY': 'test_gemini_key_12345678',
            'OPENAI_API_KEY': 'test_openai_key_12345678',
            'PROJECT_ID': 'test_project_id',
            'EMBEDDING_MODEL': 'text-embedding-ada-002'
        })
        self.env_patcher.start()

        # Mock only external API calls
        self.genai_patcher = patch('main.genai')
        self.openai_patcher = patch('main.OpenAIClient')
        self.db_patcher = patch('main.DatabaseConnection')

        self.mock_genai = self.genai_patcher.start()
        self.mock_openai_client = self.openai_patcher.start()
        self.mock_db = self.db_patcher.start()

        # Configure API mocks
        self.mock_openai_instance = Mock()
        self.mock_openai_client.return_value = self.mock_openai_instance

        self.mock_gemini_model = Mock()
        self.mock_genai.GenerativeModel.return_value = self.mock_gemini_model

        # Setup realistic API responses
        mock_response = Mock()
        mock_response.text = """
        # Sample Document

        <chunk>
        This is the first chunk of the document containing important information
        about the topic. It has enough content to be meaningful for embedding.
        </chunk>

        <chunk>
        This is the second chunk with different content that discusses another
        aspect of the topic. It also contains sufficient text for processing.
        </chunk>
        """
        self.mock_gemini_model.generate_content.return_value = mock_response

        # Mock embedding response
        mock_embedding_response = Mock()
        mock_embedding_data = Mock()
        mock_embedding_data.embedding = [0.1] * 1536
        mock_embedding_response.data = [mock_embedding_data]
        self.mock_openai_instance.Embeddings.create.return_value = mock_embedding_response

        # Mock database
        self.mock_db_instance = Mock()
        self.mock_db.return_value.__enter__.return_value = self.mock_db_instance

    def tearDown(self):
        """Clean up integration test fixtures."""
        self.env_patcher.stop()
        self.genai_patcher.stop()
        self.openai_patcher.stop()
        self.db_patcher.stop()

    def test_end_to_end_processing(self):
        """Test complete document processing pipeline."""
        processor = DocumentProcessor()

        # Create a sample markdown file for testing
        test_content = """
        # Test Document

        <chunk>
        This is a test chunk with sample content for processing.
        It contains enough text to be meaningful for embedding generation.
        </chunk>

        <chunk>
        This is another test chunk with different content.
        It also has sufficient text for the embedding process.
        </chunk>
        """

        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_file:
            temp_file.write(test_content)
            temp_file.flush()

            try:
                # Test the complete processing pipeline
                result = processor.process_document(temp_file.name, document_id=123)

                # Verify result structure
                self.assertIn('input_path', result)
                self.assertIn('document_id', result)
                self.assertIn('markdown_path', result)
                self.assertIn('embeddings_path', result)
                self.assertIn('chunks_count', result)

                self.assertEqual(result['document_id'], 123)
                self.assertEqual(result['chunks_count'], 2)

                # Verify files were created
                self.assertTrue(os.path.exists(result['embeddings_path']))

                # Verify database operations were called
                self.mock_db_instance.execute_query.assert_called()

                # Clean up generated files
                if os.path.exists(result['embeddings_path']):
                    os.unlink(result['embeddings_path'])

            finally:
                os.unlink(temp_file.name)


def run_tests(unit_only=False, integration_only=False, verbose=False):
    """Run the test suite with specified options."""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    if not integration_only:
        logger.info("Adding unit tests...")
        suite.addTests(loader.loadTestsFromTestCase(TestDocumentProcessor))

    if not unit_only:
        logger.info("Adding integration tests...")
        suite.addTests(loader.loadTestsFromTestCase(IntegrationTestDocumentProcessor))

    runner = unittest.TextTestRunner(verbosity=2 if verbose else 1)
    result = runner.run(suite)

    return result.wasSuccessful()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Test DocumentProcessor class")
    parser.add_argument("--unit-only", action="store_true", help="Run only unit tests")
    parser.add_argument("--integration-only", action="store_true", help="Run only integration tests")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")

    args = parser.parse_args()

    logger.info("=" * 80)
    logger.info("DocumentProcessor Test Suite")
    logger.info("=" * 80)

    success = run_tests(
        unit_only=args.unit_only,
        integration_only=args.integration_only,
        verbose=args.verbose
    )

    if success:
        logger.info("All tests passed! ✅")
        sys.exit(0)
    else:
        logger.error("Some tests failed! ❌")
        sys.exit(1)
