#!/usr/bin/env python3
"""
Search Trace Demo Script

This script demonstrates how to use the search tracing functionality
with the Chinese query "列出所有庫易股票資料" as requested.

Usage:
    python run_search_trace_demo.py
"""

import os
import sys
import logging
import subprocess

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def run_search_trace_demo():
    """Run the search trace demo with the requested Chinese query."""
    logger.info("🚀 Starting Search Trace Demo")
    logger.info("=" * 60)
    
    # The Chinese query as requested by the user
    chinese_query = "列出所有庫易股票資料"
    
    logger.info(f"Testing with Chinese query: '{chinese_query}'")
    logger.info("This query means: 'List all stock data from Koo-Yi'")
    logger.info("")
    
    # Different ways to run the search tracing
    demos = [
        {
            'name': 'Basic Search Trace',
            'description': 'Run basic search tracing with the Chinese query',
            'command': ['python', 'test_search_traces.py', '--query', chinese_query]
        },
        {
            'name': 'Search Trace with Debug Output',
            'description': 'Run search tracing and save debug files',
            'command': ['python', 'test_search_traces.py', '--query', chinese_query, '--save-debug']
        },
        {
            'name': 'Web Interface Search Traces Only',
            'description': 'Run only search traces from web interface test',
            'command': ['python', 'test_web_interface.py', '--search-traces-only']
        },
        {
            'name': 'Web Interface with Custom Query',
            'description': 'Run web interface test with custom Chinese query',
            'command': ['python', 'test_web_interface.py', '--search-traces-only', '--custom-query', chinese_query]
        }
    ]
    
    logger.info("Available demo options:")
    for i, demo in enumerate(demos, 1):
        logger.info(f"{i}. {demo['name']}")
        logger.info(f"   {demo['description']}")
        logger.info(f"   Command: {' '.join(demo['command'])}")
        logger.info("")
    
    # Ask user which demo to run
    try:
        choice = input("Enter demo number to run (1-4), or 'all' to run all demos: ").strip().lower()
        
        if choice == 'all':
            logger.info("Running all demos...")
            for i, demo in enumerate(demos, 1):
                logger.info(f"\n{'='*20} DEMO {i}: {demo['name']} {'='*20}")
                run_demo(demo)
        elif choice.isdigit() and 1 <= int(choice) <= len(demos):
            demo_index = int(choice) - 1
            demo = demos[demo_index]
            logger.info(f"Running Demo {choice}: {demo['name']}")
            run_demo(demo)
        else:
            logger.error("Invalid choice. Please enter a number 1-4 or 'all'")
            return False
            
    except KeyboardInterrupt:
        logger.info("Demo interrupted by user")
        return False
    except Exception as e:
        logger.error(f"Demo failed: {str(e)}")
        return False
    
    return True

def run_demo(demo):
    """Run a specific demo."""
    try:
        logger.info(f"Executing: {' '.join(demo['command'])}")
        
        # Change to the testscripts directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        result = subprocess.run(
            demo['command'],
            cwd=script_dir,
            capture_output=True,
            text=True,
            timeout=120  # 2 minute timeout
        )
        
        if result.returncode == 0:
            logger.info(f"✅ Demo completed successfully")
            logger.info("Output:")
            print(result.stdout)
        else:
            logger.error(f"❌ Demo failed with return code {result.returncode}")
            logger.error("Error output:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        logger.error("❌ Demo timed out after 2 minutes")
    except Exception as e:
        logger.error(f"❌ Demo execution failed: {str(e)}")

def show_usage_examples():
    """Show usage examples for the search tracing functionality."""
    logger.info("📚 SEARCH TRACING USAGE EXAMPLES")
    logger.info("=" * 60)
    
    examples = [
        {
            'title': 'Test Chinese Query Only',
            'command': 'python test_search_traces.py --query "列出所有庫易股票資料"',
            'description': 'Test only the Chinese query with detailed tracing'
        },
        {
            'title': 'Save Debug Output',
            'command': 'python test_search_traces.py --query "列出所有庫易股票資料" --save-debug',
            'description': 'Test and save debug files for analysis'
        },
        {
            'title': 'Test Multiple Results',
            'command': 'python test_search_traces.py --query "列出所有庫易股票資料" --top-k 10',
            'description': 'Get more results for better analysis'
        },
        {
            'title': 'Web Interface Traces Only',
            'command': 'python test_web_interface.py --search-traces-only',
            'description': 'Run only search traces from web interface test'
        },
        {
            'title': 'Skip Search Traces in Full Test',
            'command': 'python test_web_interface.py --skip-search-traces',
            'description': 'Run full web interface test but skip detailed traces'
        }
    ]
    
    for i, example in enumerate(examples, 1):
        logger.info(f"{i}. {example['title']}")
        logger.info(f"   Command: {example['command']}")
        logger.info(f"   Description: {example['description']}")
        logger.info("")

def main():
    """Main function."""
    logger.info("🔍 Search Trace Demo")
    logger.info("This demo shows how to use search traces to understand search functionality")
    logger.info("")
    
    # Check if we're in the right directory
    if not os.path.exists('test_search_traces.py'):
        logger.error("❌ test_search_traces.py not found. Please run from testscripts directory.")
        return False
    
    try:
        choice = input("Choose option:\n1. Run interactive demo\n2. Show usage examples\n3. Exit\nEnter choice (1-3): ").strip()
        
        if choice == '1':
            return run_search_trace_demo()
        elif choice == '2':
            show_usage_examples()
            return True
        elif choice == '3':
            logger.info("Goodbye!")
            return True
        else:
            logger.error("Invalid choice")
            return False
            
    except KeyboardInterrupt:
        logger.info("Demo interrupted by user")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"Demo failed: {str(e)}")
        sys.exit(1)
