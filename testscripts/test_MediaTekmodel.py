import torch
import requests
import json
import argparse
from transformers import AutoModelForCausalLM, AutoTokenizer

# Function to test MediaTek model
def test_mediatek_model():
    try:
        # Set model path and cache directory
        model_name = "MediaTek-Research/Breeze-7B-Instruct-v0_1"
        cache_dir = "/Volumes/X10Pro/LLM/"
        
        print(f"Loading model {model_name}...")
        print(f"Using device: {'cuda' if torch.cuda.is_available() else 'cpu'}")
        
        # Load tokenizer and model with proper cache directory
        tokenizer = AutoTokenizer.from_pretrained(model_name, cache_dir=cache_dir)
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            cache_dir=cache_dir,
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            device_map="auto"
        )
        
        # Input text
        input_text = "請寫出一個計算機器學習模型的代碼"
        print(f"\nInput: {input_text}")
        
        # Generate response
        inputs = tokenizer(input_text, return_tensors="pt")
        if torch.cuda.is_available():
            inputs = {k: v.cuda() for k, v in inputs.items()}
        
        # Generate with parameters appropriate for the model
        output = model.generate(
            **inputs,
            max_new_tokens=512,
            temperature=0.7,
            top_p=0.9,
            do_sample=True
        )
        
        # Decode and print the response
        response = tokenizer.decode(output[0], skip_special_tokens=True)
        print(f"\nOutput: {response}")
        
    except Exception as e:
        print(f"Error in MediaTek model test: {e}")

# Function to test Ollama model
def test_ollama_model(model_name="llama3.1:latest", host="http://localhost:11434"):
    try:
        print(f"\n--- Testing Ollama Model: {model_name} ---")
        
        # API endpoint for Ollama
        api_url = f"{host}/api/generate"
        
        # Input text - using the same as MediaTek test for comparison
        input_text = "請寫出一個SQL查詢的代碼"
        print(f"Input: {input_text}")
        
        # Prepare the request payload
        payload = {
            "model": model_name,
            "prompt": input_text,
            "stream": False,
            "options": {
                "temperature": 0.7,
                "top_p": 0.9,
                "max_tokens": 512
            }
        }
        
        # Send request to Ollama API
        print(f"Sending request to Ollama API at {api_url}...")
        response = requests.post(api_url, json=payload)
        
        # Check if request was successful
        if response.status_code == 200:
            result = response.json()
            output_text = result.get("response", "")
            print(f"\nOutput: {output_text}")
            
            # Print additional information if available
            if "eval_count" in result:
                print(f"\nTokens generated: {result['eval_count']}")
            if "eval_duration" in result:
                print(f"Generation time: {result['eval_duration']/1000000:.2f} seconds")
        else:
            print(f"Error: API returned status code {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"Error in Ollama model test: {e}")

# Main function to parse arguments and run tests
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test LLM models")
    parser.add_argument("--model", choices=["mediatek", "ollama", "both"], default="both",
                        help="Which model to test (mediatek, ollama, or both)")
    parser.add_argument("--ollama-model", default="llama3.1:latest", 
                        help="Ollama model name to use (default: llama3.1:latest)")
    parser.add_argument("--ollama-host", default="http://localhost:11434",
                        help="Ollama API host (default: http://localhost:11434)")
    
    args = parser.parse_args()
    
    if args.model in ["mediatek", "both"]:
        print("\n=== Testing MediaTek Model ===")
        test_mediatek_model()
        
    if args.model in ["ollama", "both"]:
        print("\n=== Testing Ollama Model ===")
        test_ollama_model(model_name=args.ollama_model, host=args.ollama_host)