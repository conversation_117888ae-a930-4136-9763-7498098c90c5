#!/bin/bash
# Simple wrapper script for running API tests with appropriate options

# Default options
SKIP_PYTHON=false
SKIP_CURL=false
SKIP_PDF=false
QUICK_ONLY=false
TIMEOUT=30

# Help message
show_help() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -h, --help           Show this help message"
    echo "  -q, --quick          Run only quick tests"
    echo "  --skip-python        Skip Python tests"
    echo "  --skip-curl          Skip curl tests"
    echo "  --skip-pdf           Skip PDF creation"
    echo "  --timeout SECONDS    Set timeout for tests (default: 30)"
    echo ""
    echo "Examples:"
    echo "  $0 --quick           # Run only quick tests"
    echo "  $0 --skip-python     # Run all tests except Python tests"
    echo "  $0 --skip-curl       # Run all tests except curl tests"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -q|--quick)
            QUICK_ONLY=true
            shift
            ;;
        --skip-python)
            SKIP_PYTHON=true
            shift
            ;;
        --skip-curl)
            SKIP_CURL=true
            shift
            ;;
        --skip-pdf)
            SKIP_PDF=true
            shift
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Build command
CMD="uv run $(dirname "$0")/run_api_tests.py"

if [ "$QUICK_ONLY" = true ]; then
    CMD="$CMD --quick-only"
fi

if [ "$SKIP_PYTHON" = true ]; then
    CMD="$CMD --skip-python"
fi

if [ "$SKIP_CURL" = true ]; then
    CMD="$CMD --skip-curl"
fi

if [ "$SKIP_PDF" = true ]; then
    CMD="$CMD --skip-pdf-creation"
fi

# Run the command
echo "Running: $CMD"
eval "$CMD"
