# DocumentProcessor Test Suite

This directory contains comprehensive tests for the `DocumentProcessor` class from `main.py`. The test suite includes both unit tests with mocked dependencies and integration tests with real file operations.

## Files

- `test_document_processor.py` - Main test suite with unit and integration tests
- `run_document_processor_tests.py` - Test runner script with additional utilities
- `TEST_DOCUMENT_PROCESSOR_README.md` - This documentation file

## Test Coverage

### Unit Tests (`TestDocumentProcessor`)

The unit tests use mocked dependencies to test individual methods in isolation:

1. **Initialization Tests**
   - `test_init_success()` - Tests successful initialization with valid environment variables
   - `test_init_missing_env_vars()` - Tests failure when required environment variables are missing

2. **PDF Processing Tests**
   - `test_get_chunks_success()` - Tests successful PDF to markdown conversion
   - `test_get_chunks_pdf_error()` - Tests error handling during PDF processing

3. **Embedding Tests**
   - `test_create_embeddings_success()` - Tests successful embedding generation from chunks
   - `test_create_embeddings_no_chunks()` - Tests handling when no chunk tags are found

4. **Database Tests**
   - `test_insert_embeddings_to_db()` - Tests database insertion of embeddings

### Integration Tests (`IntegrationTestDocumentProcessor`)

The integration tests use real file operations but mock external APIs:

1. **End-to-End Processing**
   - `test_end_to_end_processing()` - Tests the complete document processing pipeline

## Dependencies

The test suite requires the following Python modules:
- `unittest` (built-in)
- `unittest.mock` (built-in)
- `tempfile` (built-in)
- `json` (built-in)
- `os` (built-in)
- `sys` (built-in)
- `pathlib` (built-in)
- `logging` (built-in)

## Environment Variables

The tests mock the following environment variables:
- `GEMINI_API_KEY` - Google Gemini API key
- `OPENAI_API_KEY` - OpenAI API key
- `PROJECT_ID` - Project identifier
- `EMBEDDING_MODEL` - Embedding model name (defaults to 'text-embedding-ada-002')

## Running the Tests

### Method 1: Using the Test Runner (Recommended)

```bash
# Run all tests
python run_document_processor_tests.py

# Run only unit tests
python run_document_processor_tests.py --type unit

# Run only integration tests
python run_document_processor_tests.py --type integration

# Run with verbose output
python run_document_processor_tests.py --verbose

# Create sample test data
python run_document_processor_tests.py --create-sample-data

# Skip dependency checks
python run_document_processor_tests.py --skip-checks
```

### Method 2: Direct Test Execution

```bash
# Run all tests
python test_document_processor.py

# Run only unit tests
python test_document_processor.py --unit-only

# Run only integration tests
python test_document_processor.py --integration-only

# Run with verbose output
python test_document_processor.py --verbose
```

### Method 3: Using unittest module

```bash
# Run all tests
python -m unittest test_document_processor

# Run specific test class
python -m unittest test_document_processor.TestDocumentProcessor

# Run specific test method
python -m unittest test_document_processor.TestDocumentProcessor.test_init_success

# Run with verbose output
python -m unittest -v test_document_processor
```

## Test Output

The tests provide detailed output including:
- Test execution status for each method
- Logging information about test setup and execution
- Clear success/failure indicators
- Error details when tests fail

Example successful output:
```
2024-01-15 10:30:00 [INFO] DocumentProcessor Test Suite
================================================================================
2024-01-15 10:30:00 [INFO] Adding unit tests...
2024-01-15 10:30:00 [INFO] Adding integration tests...
test_create_embeddings_no_chunks (__main__.TestDocumentProcessor) ... ok
test_create_embeddings_success (__main__.TestDocumentProcessor) ... ok
test_get_chunks_pdf_error (__main__.TestDocumentProcessor) ... ok
test_get_chunks_success (__main__.TestDocumentProcessor) ... ok
test_init_missing_env_vars (__main__.TestDocumentProcessor) ... ok
test_init_success (__main__.TestDocumentProcessor) ... ok
test_insert_embeddings_to_db (__main__.TestDocumentProcessor) ... ok
test_end_to_end_processing (__main__.IntegrationTestDocumentProcessor) ... ok

----------------------------------------------------------------------
Ran 8 tests in 0.123s

OK
2024-01-15 10:30:00 [INFO] All tests passed! ✅
```

## Mocking Strategy

The test suite uses comprehensive mocking to isolate the DocumentProcessor logic:

1. **External APIs**: OpenAI and Google Gemini APIs are mocked to avoid real API calls
2. **Database**: DatabaseConnection is mocked to avoid database dependencies
3. **File Operations**: Unit tests mock file operations, integration tests use real files
4. **Environment Variables**: All required environment variables are mocked

## Test Data

The test suite creates temporary files for testing:
- Temporary PDF files for PDF processing tests
- Temporary markdown files with chunk tags
- Temporary JSON files for embedding data
- All temporary files are automatically cleaned up after tests

## Troubleshooting

### Common Issues

1. **Import Errors**
   - Ensure you're running the tests from the project root directory
   - Check that `main.py` exists in the current directory

2. **Environment Variable Errors**
   - The tests mock all required environment variables
   - If you see environment variable errors, check the test setup

3. **File Permission Errors**
   - Ensure the test directory has write permissions for temporary files

4. **Module Not Found Errors**
   - Check that all required dependencies are installed
   - Use `pip install -r requirements.txt` to install project dependencies

### Debug Mode

For debugging test failures, run with verbose output:
```bash
python test_document_processor.py --verbose
```

This will provide detailed logging information about test execution.

## Contributing

When adding new tests:
1. Follow the existing naming convention (`test_method_name_scenario`)
2. Add appropriate docstrings explaining what the test does
3. Use proper mocking to isolate the code under test
4. Clean up any temporary files or resources
5. Update this README if adding new test categories

## Future Enhancements

Potential improvements to the test suite:
1. Add performance benchmarking tests
2. Add tests for concurrent processing
3. Add tests for different file formats
4. Add property-based testing with hypothesis
5. Add test coverage reporting
