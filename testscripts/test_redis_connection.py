#!/usr/bin/env python3
"""
Simple Redis Connection Test

This script tests the Redis connection to ensure it's working
properly for the PDF processing API.
"""

import socket
import time
import sys
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def test_redis_socket(host='localhost', port=6379, timeout=5):
    """Test Redis connection using socket."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        logger.error(f"Socket test failed: {e}")
        return False


def test_redis_ping(host='localhost', port=6379, timeout=5):
    """Test Redis PING command."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        sock.connect((host, port))
        
        # Send PING command
        sock.send(b'PING\r\n')
        response = sock.recv(1024).decode('utf-8')
        sock.close()
        
        return '+PONG' in response
    except Exception as e:
        logger.error(f"PING test failed: {e}")
        return False


def test_redis_with_library():
    """Test Redis using redis-py library if available."""
    try:
        import redis
        
        # Try to connect
        r = redis.Redis(host='localhost', port=6379, db=0, 
                       socket_timeout=5, socket_connect_timeout=5)
        
        # Test ping
        response = r.ping()
        if response:
            logger.info("✓ Redis library test: PING successful")
            
            # Test basic operations
            r.set('test_key', 'test_value', ex=10)  # Expires in 10 seconds
            value = r.get('test_key')
            if value and value.decode('utf-8') == 'test_value':
                logger.info("✓ Redis library test: SET/GET successful")
                r.delete('test_key')
                return True
            else:
                logger.warning("✗ Redis library test: SET/GET failed")
                return False
        else:
            logger.error("✗ Redis library test: PING failed")
            return False
            
    except ImportError:
        logger.warning("redis-py library not available")
        logger.info("Install with: pip install redis")
        return None
    except Exception as e:
        logger.error(f"Redis library test failed: {e}")
        return False


def test_celery_connection():
    """Test Celery connection to Redis."""
    try:
        from celery import Celery
        
        # Create test Celery app
        app = Celery('test', broker='redis://localhost:6379/0')
        
        # Test connection
        inspect = app.control.inspect()
        stats = inspect.stats()
        
        if stats is not None:
            logger.info("✓ Celery connection test: successful")
            return True
        else:
            logger.warning("✗ Celery connection test: no workers found")
            return False
            
    except ImportError:
        logger.warning("Celery library not available")
        return None
    except Exception as e:
        logger.error(f"Celery connection test failed: {e}")
        return False


def main():
    """Main test function."""
    logger.info("=" * 50)
    logger.info("Redis Connection Test")
    logger.info("=" * 50)
    
    host = 'localhost'
    port = 6379
    
    # Test 1: Socket connection
    logger.info(f"Testing socket connection to {host}:{port}...")
    if test_redis_socket(host, port):
        logger.info("✓ Socket connection: successful")
        socket_ok = True
    else:
        logger.error("✗ Socket connection: failed")
        logger.error("Redis is not running or not accessible")
        socket_ok = False
    
    if not socket_ok:
        logger.info("\nTo start Redis:")
        logger.info("  Docker: python3 setup_docker_redis.py --mode redis-only")
        logger.info("  macOS: brew install redis && brew services start redis")
        logger.info("  Ubuntu: sudo apt-get install redis-server && sudo systemctl start redis-server")
        sys.exit(1)
    
    # Test 2: Redis PING
    logger.info("\nTesting Redis PING command...")
    if test_redis_ping(host, port):
        logger.info("✓ Redis PING: successful")
    else:
        logger.error("✗ Redis PING: failed")
        logger.error("Redis is running but not responding correctly")
    
    # Test 3: Redis library
    logger.info("\nTesting with redis-py library...")
    redis_lib_result = test_redis_with_library()
    if redis_lib_result is True:
        logger.info("✓ Redis library: all tests passed")
    elif redis_lib_result is False:
        logger.error("✗ Redis library: tests failed")
    else:
        logger.info("- Redis library: not available (optional)")
    
    # Test 4: Celery connection
    logger.info("\nTesting Celery connection...")
    celery_result = test_celery_connection()
    if celery_result is True:
        logger.info("✓ Celery connection: successful")
    elif celery_result is False:
        logger.warning("✗ Celery connection: failed (no workers running)")
        logger.info("This is normal if Celery workers are not started yet")
    else:
        logger.info("- Celery: not available (optional for this test)")
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("Test Summary")
    logger.info("=" * 50)
    
    if socket_ok:
        logger.info("✓ Redis is running and accessible")
        logger.info("✓ Ready for PDF processing API")
        
        if redis_lib_result is False:
            logger.warning("⚠ Consider installing redis-py: pip install redis")
        
        logger.info("\nNext steps:")
        logger.info("1. Start the API server: python3 main.py")
        logger.info("2. Run API tests: python3 test_api_python.py")
        
        sys.exit(0)
    else:
        logger.error("✗ Redis setup required")
        sys.exit(1)


if __name__ == "__main__":
    main()
