#!/usr/bin/env python3
"""
Verify that the PDF processor is using Mistral AI and not OpenAI.
"""

import os
import sys
import logging
import inspect

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def check_pdf_processor():
    """Check what the PDF processor is actually importing and using."""
    logger.info("🔍 Checking PDF Processor Implementation")
    logger.info("=" * 50)
    
    try:
        # Import the PDF processor
        from processors import process_pdf_document
        from processors.pdf import process_pdf
        
        logger.info("✅ Successfully imported PDF processor functions")
        
        # Check the source file
        source_file = inspect.getfile(process_pdf)
        logger.info(f"📁 Source file: {source_file}")
        
        # Read the source code to check for imports
        with open(source_file, 'r') as f:
            content = f.read()
        
        # Check for problematic imports
        if 'from openai import OpenAI' in content:
            logger.error("❌ FOUND OpenAI import in PDF processor!")
            return False
        elif 'import google.generativeai' in content:
            logger.error("❌ FOUND Google Generative AI import in PDF processor!")
            return False
        elif 'from mistralai import Mistral' in content:
            logger.info("✅ Found Mistral AI import - correct!")
        else:
            logger.warning("⚠️  No Mistral import found, but no OpenAI either")
        
        # Check for specific function calls
        if 'client.embeddings.create(' in content and 'OpenAI()' in content:
            logger.error("❌ FOUND OpenAI embedding calls!")
            return False
        elif 'client.embeddings.create(' in content and 'Mistral(' in content:
            logger.info("✅ Found Mistral embedding calls - correct!")
        
        # Check for Gemini calls
        if 'genai.GenerativeModel' in content:
            logger.error("❌ FOUND Google Gemini calls!")
            return False
        elif 'client.chat.complete(' in content:
            logger.info("✅ Found Mistral chat completion calls - correct!")
        
        logger.info("✅ PDF processor appears to be using Mistral AI correctly")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error checking PDF processor: {str(e)}")
        return False

def check_imports():
    """Check what gets imported when we import the processors module."""
    logger.info("\n🔍 Checking Processor Module Imports")
    logger.info("=" * 50)
    
    try:
        import processors
        
        # Check what's available
        available_functions = [attr for attr in dir(processors) if not attr.startswith('_')]
        logger.info(f"Available functions: {available_functions}")
        
        # Check the process_pdf_document function specifically
        if hasattr(processors, 'process_pdf_document'):
            func = processors.process_pdf_document
            source_file = inspect.getfile(func)
            logger.info(f"process_pdf_document source: {source_file}")
            
            # Check if it's the right function
            if 'pdf.py' in source_file and 'pdf_openai.py' not in source_file:
                logger.info("✅ process_pdf_document is from the correct file")
                return True
            else:
                logger.error(f"❌ process_pdf_document is from wrong file: {source_file}")
                return False
        else:
            logger.error("❌ process_pdf_document not found in processors module")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error checking imports: {str(e)}")
        return False

def check_worker_import():
    """Check what the worker would import."""
    logger.info("\n🔍 Checking Worker Import Path")
    logger.info("=" * 50)
    
    try:
        # Simulate the worker import
        from processors import process_pdf_document
        from processors import generate_knowledge_graph
        
        # Check the source files
        pdf_source = inspect.getfile(process_pdf_document)
        kg_source = inspect.getfile(generate_knowledge_graph)
        
        logger.info(f"Worker would import process_pdf_document from: {pdf_source}")
        logger.info(f"Worker would import generate_knowledge_graph from: {kg_source}")
        
        # Verify it's the right files
        if 'pdf.py' in pdf_source and 'pdf_openai.py' not in pdf_source:
            logger.info("✅ Worker will use the correct PDF processor")
            return True
        else:
            logger.error(f"❌ Worker will use wrong PDF processor: {pdf_source}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error checking worker imports: {str(e)}")
        return False

def main():
    """Main function."""
    logger.info("🚀 Verifying PDF Processor Configuration")
    
    tests = [
        ("PDF Processor Implementation", check_pdf_processor),
        ("Module Imports", check_imports),
        ("Worker Import Path", check_worker_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {str(e)}")
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("VERIFICATION SUMMARY")
    logger.info(f"{'='*60}")
    logger.info(f"Tests passed: {passed}/{total}")
    logger.info(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        logger.info("🎉 ALL CHECKS PASSED!")
        logger.info("   The PDF processor is correctly configured to use Mistral AI.")
        logger.info("   If you're still seeing OpenAI errors, restart your Docker containers.")
    else:
        logger.warning("⚠️  Some checks failed.")
        logger.info("   There may be configuration issues that need to be resolved.")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Verification interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Verification failed: {str(e)}")
        sys.exit(1)
