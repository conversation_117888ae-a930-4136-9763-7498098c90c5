#!/usr/bin/env python3
"""
Web Interface Test Script

This script tests the Flask web interface functionality by:
1. Checking if the web interface is running
2. Testing API connectivity from the web interface
3. Verifying that search functionality works
4. Testing document upload capabilities
5. Checking knowledge base data retrieval

Usage:
    python test_web_interface.py
    python test_web_interface.py --web-url http://localhost:5000 --api-url http://localhost:8000
"""

import os
import sys
import requests
import time
import json
import logging
from pathlib import Path
import argparse

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class WebInterfaceTestRunner:
    """Test runner for the Flask web interface."""
    
    def __init__(self, web_url: str = "http://localhost:5000", api_url: str = "http://localhost:8000"):
        self.web_url = web_url.rstrip('/')
        self.api_url = api_url.rstrip('/')
        self.api_key = os.getenv('API_KEY', 'a614192a822e6daef18a68029396879632c768dac57fe826043cf785bcdf519a7')
        self.test_results = {}
        self.skip_search_traces = False
        
    def test_web_interface_availability(self) -> bool:
        """Test if the web interface is running and accessible."""
        logger.info("Testing web interface availability...")
        
        try:
            response = requests.get(f"{self.web_url}/", timeout=10)
            if response.status_code == 200:
                logger.info("✅ Web interface is accessible")
                self.test_results['web_interface_available'] = True
                return True
            else:
                logger.error(f"❌ Web interface returned status {response.status_code}")
                self.test_results['web_interface_available'] = False
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Failed to connect to web interface: {e}")
            logger.info("💡 Make sure the Flask web interface is running:")
            logger.info("   python start_web.py")
            self.test_results['web_interface_available'] = False
            return False
    
    def test_api_connectivity(self) -> bool:
        """Test if the API backend is accessible."""
        logger.info("Testing API backend connectivity...")
        
        try:
            headers = {'X-API-Key': self.api_key}
            response = requests.get(f"{self.api_url}/health", headers=headers, timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                logger.info("✅ API backend is accessible")
                logger.info(f"   API Status: {health_data.get('status', 'unknown')}")
                logger.info(f"   Database: {health_data.get('database', 'unknown')}")
                self.test_results['api_connectivity'] = True
                return True
            else:
                logger.error(f"❌ API returned status {response.status_code}")
                self.test_results['api_connectivity'] = False
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Failed to connect to API: {e}")
            logger.info("💡 Make sure the API backend is running:")
            logger.info("   docker-compose up -d")
            self.test_results['api_connectivity'] = False
            return False
    
    def test_knowledge_base_data(self) -> bool:
        """Test knowledge base data retrieval."""
        logger.info("Testing knowledge base data retrieval...")
        
        try:
            headers = {'X-API-Key': self.api_key}
            response = requests.get(f"{self.api_url}/kbdata", headers=headers, timeout=10)
            
            if response.status_code == 200:
                kb_data = response.json()
                stats = kb_data.get('stats', {})
                
                logger.info("✅ Knowledge base data retrieved successfully")
                logger.info(f"   Total Documents: {stats.get('total_documents', 0)}")
                logger.info(f"   Total Chunks: {stats.get('total_chunks', 0)}")
                logger.info(f"   Total Entities: {stats.get('total_entities', 0)}")
                logger.info(f"   Total Relationships: {stats.get('total_relationships', 0)}")
                
                self.test_results['kb_data_retrieval'] = True
                return True
            else:
                logger.error(f"❌ Knowledge base data request failed with status {response.status_code}")
                self.test_results['kb_data_retrieval'] = False
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Failed to retrieve knowledge base data: {e}")
            self.test_results['kb_data_retrieval'] = False
            return False
    
    def test_search_functionality(self) -> bool:
        """Test search functionality through the API."""
        logger.info("Testing search functionality...")

        try:
            headers = {
                'X-API-Key': self.api_key,
                'Content-Type': 'application/json'
            }

            search_data = {
                'query': 'test search query',
                'top_k': 3,
                'debug': False
            }

            response = requests.post(
                f"{self.api_url}/kag-search",
                headers=headers,
                json=search_data,
                timeout=30
            )

            if response.status_code == 200:
                search_results = response.json()
                results = search_results.get('results', [])

                logger.info("✅ Search functionality is working")
                logger.info(f"   Query: {search_results.get('query', 'N/A')}")
                logger.info(f"   Results found: {len(results)}")
                logger.info(f"   Execution time: {search_results.get('execution_time', 0):.2f}s")

                if search_results.get('generated_response'):
                    logger.info("   ✅ AI response generation working")
                else:
                    logger.warning("   ⚠️  No AI response generated")

                self.test_results['search_functionality'] = True
                return True
            else:
                logger.error(f"❌ Search request failed with status {response.status_code}")
                if response.text:
                    logger.error(f"   Error: {response.text}")
                self.test_results['search_functionality'] = False
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Search functionality test failed: {e}")
            self.test_results['search_functionality'] = False
            return False

    def test_search_with_traces(self) -> bool:
        """Test search functionality with detailed traces to understand how search works."""
        logger.info("=" * 80)
        logger.info("🔍 DETAILED SEARCH TRACING TEST")
        logger.info("=" * 80)

        # Test queries including Chinese query
        test_queries = [
            "列出所有庫易股票資料",  # Chinese query as requested
            "What is Docker and how does it work?",
            "financial data analysis",
            "stock market information"
        ]

        all_tests_passed = True

        for i, query in enumerate(test_queries, 1):
            logger.info(f"\n📋 TEST {i}/{len(test_queries)}: Testing query: '{query}'")
            logger.info("-" * 60)

            if not self._trace_single_search(query):
                all_tests_passed = False

        self.test_results['search_with_traces'] = all_tests_passed
        return all_tests_passed

    def _trace_single_search(self, query: str) -> bool:
        """Perform detailed tracing of a single search query."""
        try:
            import time
            start_time = time.time()

            headers = {
                'X-API-Key': self.api_key,
                'Content-Type': 'application/json'
            }

            # Enable debug mode for detailed tracing
            search_data = {
                'query': query,
                'top_k': 5,
                'debug': True  # Enable debug mode
            }

            logger.info(f"🚀 Starting search for: '{query}'")
            logger.info(f"   Request payload: {search_data}")

            # Make the search request
            response = requests.post(
                f"{self.api_url}/kag-search",
                headers=headers,
                json=search_data,
                timeout=60  # Longer timeout for detailed analysis
            )

            request_time = time.time() - start_time
            logger.info(f"⏱️  API request completed in {request_time:.3f}s")

            if response.status_code != 200:
                logger.error(f"❌ Search failed with status {response.status_code}")
                if response.text:
                    logger.error(f"   Error details: {response.text}")
                return False

            # Parse response
            search_results = response.json()
            results = search_results.get('results', [])

            # Log basic search results
            logger.info(f"✅ Search completed successfully")
            logger.info(f"   📊 SEARCH SUMMARY:")
            logger.info(f"      • Query: {search_results.get('query', 'N/A')}")
            logger.info(f"      • Results found: {len(results)}")
            logger.info(f"      • Total execution time: {search_results.get('execution_time', 0):.3f}s")
            logger.info(f"      • API response time: {request_time:.3f}s")

            # Analyze search results in detail
            self._analyze_search_results(query, results)

            # Check AI response generation
            generated_response = search_results.get('generated_response', '')
            if generated_response:
                logger.info(f"   🤖 AI RESPONSE GENERATED:")
                logger.info(f"      • Response length: {len(generated_response)} characters")
                logger.info(f"      • Response preview: {generated_response[:200]}...")
            else:
                logger.warning(f"   ⚠️  No AI response generated")

            # Log debug information if available
            debug_info = search_results.get('debug_info', {})
            if debug_info:
                self._log_debug_information(debug_info)

            return True

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Network error during search: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error during search tracing: {e}")
            return False

    def _analyze_search_results(self, query: str, results: list) -> None:
        """Analyze and log detailed information about search results."""
        if not results:
            logger.warning("   📭 No search results to analyze")
            return

        logger.info(f"   📈 DETAILED RESULTS ANALYSIS:")

        # Analyze score distribution
        vector_scores = [r.get('vector_score', 0) for r in results]
        text_scores = [r.get('text_score', 0) for r in results]
        combined_scores = [r.get('combined_score', 0) for r in results]

        logger.info(f"      • Vector scores: min={min(vector_scores):.4f}, max={max(vector_scores):.4f}, avg={sum(vector_scores)/len(vector_scores):.4f}")
        logger.info(f"      • Text scores: min={min(text_scores):.4f}, max={max(text_scores):.4f}, avg={sum(text_scores)/len(text_scores):.4f}")
        logger.info(f"      • Combined scores: min={min(combined_scores):.4f}, max={max(combined_scores):.4f}, avg={sum(combined_scores)/len(combined_scores):.4f}")

        # Analyze top results
        logger.info(f"      • TOP 3 RESULTS BREAKDOWN:")
        for i, result in enumerate(results[:3], 1):
            doc_id = result.get('doc_id', 'N/A')
            content = result.get('content', '')
            vector_score = result.get('vector_score', 0)
            text_score = result.get('text_score', 0)
            combined_score = result.get('combined_score', 0)
            entities = result.get('entities', [])
            relationships = result.get('relationships', [])

            logger.info(f"        Result #{i}:")
            logger.info(f"          - Doc ID: {doc_id}")
            logger.info(f"          - Vector Score: {vector_score:.4f}")
            logger.info(f"          - Text Score: {text_score:.4f}")
            logger.info(f"          - Combined Score: {combined_score:.4f}")
            logger.info(f"          - Content length: {len(content)} chars")
            logger.info(f"          - Content preview: {content[:150]}...")
            logger.info(f"          - Entities found: {len(entities)}")
            logger.info(f"          - Relationships found: {len(relationships)}")

            # Log entities if present
            if entities:
                entity_names = [e.get('name', 'Unknown') for e in entities[:3]]
                logger.info(f"          - Top entities: {', '.join(entity_names)}")

        # Analyze search effectiveness for Chinese queries
        if any('\u4e00' <= char <= '\u9fff' for char in query):
            logger.info(f"      • CHINESE QUERY ANALYSIS:")
            chinese_content_count = 0
            for result in results:
                content = result.get('content', '')
                if any('\u4e00' <= char <= '\u9fff' for char in content):
                    chinese_content_count += 1

            logger.info(f"        - Results with Chinese content: {chinese_content_count}/{len(results)}")
            logger.info(f"        - Chinese content ratio: {(chinese_content_count/len(results)*100):.1f}%")

    def _log_debug_information(self, debug_info: dict) -> None:
        """Log detailed debug information from the search engine."""
        logger.info(f"   🔧 DEBUG INFORMATION:")

        # Log search component performance
        if 'timing' in debug_info:
            timing = debug_info['timing']
            logger.info(f"      • TIMING BREAKDOWN:")
            for component, time_taken in timing.items():
                logger.info(f"        - {component}: {time_taken:.3f}s")

        # Log search method results
        if 'search_methods' in debug_info:
            methods = debug_info['search_methods']
            logger.info(f"      • SEARCH METHODS:")
            for method, info in methods.items():
                result_count = info.get('result_count', 0)
                avg_score = info.get('average_score', 0)
                logger.info(f"        - {method}: {result_count} results, avg score: {avg_score:.4f}")

        # Log query processing
        if 'query_processing' in debug_info:
            processing = debug_info['query_processing']
            logger.info(f"      • QUERY PROCESSING:")
            logger.info(f"        - Original query: {processing.get('original_query', 'N/A')}")
            logger.info(f"        - Enhanced query: {processing.get('enhanced_query', 'N/A')}")
            logger.info(f"        - Embedding dimensions: {processing.get('embedding_dimensions', 'N/A')}")

        # Log any errors or warnings
        if 'warnings' in debug_info:
            warnings = debug_info['warnings']
            logger.info(f"      • WARNINGS:")
            for warning in warnings:
                logger.warning(f"        - {warning}")

        if 'errors' in debug_info:
            errors = debug_info['errors']
            logger.info(f"      • ERRORS:")
            for error in errors:
                logger.error(f"        - {error}")
    
    def test_web_interface_pages(self) -> bool:
        """Test that all web interface pages are accessible."""
        logger.info("Testing web interface pages...")
        
        pages_to_test = [
            ('/', 'Dashboard'),
            ('/upload', 'Upload Page'),
            ('/search', 'Search Page'),
            ('/documents', 'Documents Page'),
            ('/graph', 'Knowledge Graph Page')
        ]
        
        all_pages_working = True
        
        for path, name in pages_to_test:
            try:
                response = requests.get(f"{self.web_url}{path}", timeout=10)
                if response.status_code == 200:
                    logger.info(f"   ✅ {name} accessible")
                else:
                    logger.error(f"   ❌ {name} returned status {response.status_code}")
                    all_pages_working = False
            except requests.exceptions.RequestException as e:
                logger.error(f"   ❌ {name} failed: {e}")
                all_pages_working = False
        
        if all_pages_working:
            logger.info("✅ All web interface pages are accessible")
        else:
            logger.error("❌ Some web interface pages are not accessible")
        
        self.test_results['web_pages_accessible'] = all_pages_working
        return all_pages_working
    
    def test_web_to_api_integration(self) -> bool:
        """Test that the web interface can communicate with the API."""
        logger.info("Testing web interface to API integration...")
        
        # This test simulates what the web interface does when making API calls
        try:
            # Test the same API endpoints that the web interface uses
            api_endpoints = [
                ('/health', 'Health Check'),
                ('/kbdata', 'Knowledge Base Data')
            ]
            
            all_endpoints_working = True
            headers = {'X-API-Key': self.api_key}
            
            for endpoint, name in api_endpoints:
                try:
                    response = requests.get(f"{self.api_url}{endpoint}", headers=headers, timeout=10)
                    if response.status_code == 200:
                        logger.info(f"   ✅ {name} endpoint working")
                    else:
                        logger.error(f"   ❌ {name} endpoint returned status {response.status_code}")
                        all_endpoints_working = False
                except requests.exceptions.RequestException as e:
                    logger.error(f"   ❌ {name} endpoint failed: {e}")
                    all_endpoints_working = False
            
            if all_endpoints_working:
                logger.info("✅ Web interface to API integration is working")
            else:
                logger.error("❌ Web interface to API integration has issues")
            
            self.test_results['web_api_integration'] = all_endpoints_working
            return all_endpoints_working
            
        except Exception as e:
            logger.error(f"❌ Web to API integration test failed: {e}")
            self.test_results['web_api_integration'] = False
            return False
    
    def print_summary(self) -> None:
        """Print test results summary."""
        logger.info("=" * 60)
        logger.info("Web Interface Test Results Summary")
        logger.info("=" * 60)
        
        if not self.test_results:
            logger.info("No tests were run")
            return
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            test_display = test_name.replace('_', ' ').title()
            logger.info(f"{test_display:.<40} {status}")
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        logger.info("-" * 60)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        
        if passed_tests == total_tests:
            logger.info("🎉 All tests passed! Web interface is working correctly.")
        else:
            logger.warning("⚠️  Some tests failed. Check the logs above for details.")
        
        logger.info("=" * 60)
    
    def run_all_tests(self) -> bool:
        """Run all web interface tests."""
        logger.info("=" * 60)
        logger.info("Starting Web Interface Test Suite")
        logger.info("=" * 60)
        logger.info(f"Web Interface URL: {self.web_url}")
        logger.info(f"API Backend URL: {self.api_url}")
        logger.info("=" * 60)

        success = True

        # Test web interface availability
        if not self.test_web_interface_availability():
            success = False

        # Test API connectivity
        if not self.test_api_connectivity():
            success = False

        # Test knowledge base data
        if not self.test_knowledge_base_data():
            success = False

        # Test basic search functionality
        if not self.test_search_functionality():
            success = False

        # Test detailed search with traces (unless skipped)
        if not self.skip_search_traces:
            if not self.test_search_with_traces():
                success = False
        else:
            logger.info("⏭️  Skipping detailed search traces (--skip-search-traces flag used)")

        # Test web interface pages
        if not self.test_web_interface_pages():
            success = False

        # Test web to API integration
        if not self.test_web_to_api_integration():
            success = False

        # Print summary
        self.print_summary()

        return success


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test the Flask web interface")
    parser.add_argument("--web-url", default="http://localhost:5000",
                       help="Web interface URL")
    parser.add_argument("--api-url", default="http://localhost:8000",
                       help="API backend URL")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Verbose output")
    parser.add_argument("--search-traces-only", action="store_true",
                       help="Run only the detailed search tracing tests")
    parser.add_argument("--skip-search-traces", action="store_true",
                       help="Skip the detailed search tracing tests")
    parser.add_argument("--custom-query", type=str,
                       help="Test with a custom search query")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create test runner
    runner = WebInterfaceTestRunner(web_url=args.web_url, api_url=args.api_url)

    # Handle special modes
    if args.search_traces_only:
        logger.info("🔍 Running SEARCH TRACES ONLY mode")
        if args.custom_query:
            logger.info(f"Using custom query: '{args.custom_query}'")
            success = runner._trace_single_search(args.custom_query)
        else:
            success = runner.test_search_with_traces()
        sys.exit(0 if success else 1)

    # Set skip flag for regular test run
    runner.skip_search_traces = args.skip_search_traces

    # Run all tests
    success = runner.run_all_tests()

    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
