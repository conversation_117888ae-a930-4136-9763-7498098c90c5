#!/usr/bin/env python3
"""
Web Interface Test Script

This script tests the Flask web interface functionality by:
1. Checking if the web interface is running
2. Testing API connectivity from the web interface
3. Verifying that search functionality works
4. Testing document upload capabilities
5. Checking knowledge base data retrieval

Usage:
    python test_web_interface.py
    python test_web_interface.py --web-url http://localhost:5000 --api-url http://localhost:8000
"""

import os
import sys
import requests
import time
import json
import logging
from pathlib import Path
import argparse

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class WebInterfaceTestRunner:
    """Test runner for the Flask web interface."""
    
    def __init__(self, web_url: str = "http://localhost:5000", api_url: str = "http://localhost:8000"):
        self.web_url = web_url.rstrip('/')
        self.api_url = api_url.rstrip('/')
        self.api_key = os.getenv('API_KEY', 'a614192a822e6daef18a68029396879632c768dac57fe826043cf785bcdf519a7')
        self.test_results = {}
        
    def test_web_interface_availability(self) -> bool:
        """Test if the web interface is running and accessible."""
        logger.info("Testing web interface availability...")
        
        try:
            response = requests.get(f"{self.web_url}/", timeout=10)
            if response.status_code == 200:
                logger.info("✅ Web interface is accessible")
                self.test_results['web_interface_available'] = True
                return True
            else:
                logger.error(f"❌ Web interface returned status {response.status_code}")
                self.test_results['web_interface_available'] = False
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Failed to connect to web interface: {e}")
            logger.info("💡 Make sure the Flask web interface is running:")
            logger.info("   python start_web.py")
            self.test_results['web_interface_available'] = False
            return False
    
    def test_api_connectivity(self) -> bool:
        """Test if the API backend is accessible."""
        logger.info("Testing API backend connectivity...")
        
        try:
            headers = {'X-API-Key': self.api_key}
            response = requests.get(f"{self.api_url}/health", headers=headers, timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                logger.info("✅ API backend is accessible")
                logger.info(f"   API Status: {health_data.get('status', 'unknown')}")
                logger.info(f"   Database: {health_data.get('database', 'unknown')}")
                self.test_results['api_connectivity'] = True
                return True
            else:
                logger.error(f"❌ API returned status {response.status_code}")
                self.test_results['api_connectivity'] = False
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Failed to connect to API: {e}")
            logger.info("💡 Make sure the API backend is running:")
            logger.info("   docker-compose up -d")
            self.test_results['api_connectivity'] = False
            return False
    
    def test_knowledge_base_data(self) -> bool:
        """Test knowledge base data retrieval."""
        logger.info("Testing knowledge base data retrieval...")
        
        try:
            headers = {'X-API-Key': self.api_key}
            response = requests.get(f"{self.api_url}/kbdata", headers=headers, timeout=10)
            
            if response.status_code == 200:
                kb_data = response.json()
                stats = kb_data.get('stats', {})
                
                logger.info("✅ Knowledge base data retrieved successfully")
                logger.info(f"   Total Documents: {stats.get('total_documents', 0)}")
                logger.info(f"   Total Chunks: {stats.get('total_chunks', 0)}")
                logger.info(f"   Total Entities: {stats.get('total_entities', 0)}")
                logger.info(f"   Total Relationships: {stats.get('total_relationships', 0)}")
                
                self.test_results['kb_data_retrieval'] = True
                return True
            else:
                logger.error(f"❌ Knowledge base data request failed with status {response.status_code}")
                self.test_results['kb_data_retrieval'] = False
                return False
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Failed to retrieve knowledge base data: {e}")
            self.test_results['kb_data_retrieval'] = False
            return False
    
    def test_search_functionality(self) -> bool:
        """Test search functionality through the API."""
        logger.info("Testing search functionality...")
        
        try:
            headers = {
                'X-API-Key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            search_data = {
                'query': 'test search query',
                'top_k': 3,
                'debug': False
            }
            
            response = requests.post(
                f"{self.api_url}/kag-search", 
                headers=headers, 
                json=search_data, 
                timeout=30
            )
            
            if response.status_code == 200:
                search_results = response.json()
                results = search_results.get('results', [])
                
                logger.info("✅ Search functionality is working")
                logger.info(f"   Query: {search_results.get('query', 'N/A')}")
                logger.info(f"   Results found: {len(results)}")
                logger.info(f"   Execution time: {search_results.get('execution_time', 0):.2f}s")
                
                if search_results.get('generated_response'):
                    logger.info("   ✅ AI response generation working")
                else:
                    logger.warning("   ⚠️  No AI response generated")
                
                self.test_results['search_functionality'] = True
                return True
            else:
                logger.error(f"❌ Search request failed with status {response.status_code}")
                if response.text:
                    logger.error(f"   Error: {response.text}")
                self.test_results['search_functionality'] = False
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Search functionality test failed: {e}")
            self.test_results['search_functionality'] = False
            return False
    
    def test_web_interface_pages(self) -> bool:
        """Test that all web interface pages are accessible."""
        logger.info("Testing web interface pages...")
        
        pages_to_test = [
            ('/', 'Dashboard'),
            ('/upload', 'Upload Page'),
            ('/search', 'Search Page'),
            ('/documents', 'Documents Page'),
            ('/graph', 'Knowledge Graph Page')
        ]
        
        all_pages_working = True
        
        for path, name in pages_to_test:
            try:
                response = requests.get(f"{self.web_url}{path}", timeout=10)
                if response.status_code == 200:
                    logger.info(f"   ✅ {name} accessible")
                else:
                    logger.error(f"   ❌ {name} returned status {response.status_code}")
                    all_pages_working = False
            except requests.exceptions.RequestException as e:
                logger.error(f"   ❌ {name} failed: {e}")
                all_pages_working = False
        
        if all_pages_working:
            logger.info("✅ All web interface pages are accessible")
        else:
            logger.error("❌ Some web interface pages are not accessible")
        
        self.test_results['web_pages_accessible'] = all_pages_working
        return all_pages_working
    
    def test_web_to_api_integration(self) -> bool:
        """Test that the web interface can communicate with the API."""
        logger.info("Testing web interface to API integration...")
        
        # This test simulates what the web interface does when making API calls
        try:
            # Test the same API endpoints that the web interface uses
            api_endpoints = [
                ('/health', 'Health Check'),
                ('/kbdata', 'Knowledge Base Data')
            ]
            
            all_endpoints_working = True
            headers = {'X-API-Key': self.api_key}
            
            for endpoint, name in api_endpoints:
                try:
                    response = requests.get(f"{self.api_url}{endpoint}", headers=headers, timeout=10)
                    if response.status_code == 200:
                        logger.info(f"   ✅ {name} endpoint working")
                    else:
                        logger.error(f"   ❌ {name} endpoint returned status {response.status_code}")
                        all_endpoints_working = False
                except requests.exceptions.RequestException as e:
                    logger.error(f"   ❌ {name} endpoint failed: {e}")
                    all_endpoints_working = False
            
            if all_endpoints_working:
                logger.info("✅ Web interface to API integration is working")
            else:
                logger.error("❌ Web interface to API integration has issues")
            
            self.test_results['web_api_integration'] = all_endpoints_working
            return all_endpoints_working
            
        except Exception as e:
            logger.error(f"❌ Web to API integration test failed: {e}")
            self.test_results['web_api_integration'] = False
            return False
    
    def print_summary(self) -> None:
        """Print test results summary."""
        logger.info("=" * 60)
        logger.info("Web Interface Test Results Summary")
        logger.info("=" * 60)
        
        if not self.test_results:
            logger.info("No tests were run")
            return
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            test_display = test_name.replace('_', ' ').title()
            logger.info(f"{test_display:.<40} {status}")
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        logger.info("-" * 60)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        
        if passed_tests == total_tests:
            logger.info("🎉 All tests passed! Web interface is working correctly.")
        else:
            logger.warning("⚠️  Some tests failed. Check the logs above for details.")
        
        logger.info("=" * 60)
    
    def run_all_tests(self) -> bool:
        """Run all web interface tests."""
        logger.info("=" * 60)
        logger.info("Starting Web Interface Test Suite")
        logger.info("=" * 60)
        logger.info(f"Web Interface URL: {self.web_url}")
        logger.info(f"API Backend URL: {self.api_url}")
        logger.info("=" * 60)
        
        success = True
        
        # Test web interface availability
        if not self.test_web_interface_availability():
            success = False
        
        # Test API connectivity
        if not self.test_api_connectivity():
            success = False
        
        # Test knowledge base data
        if not self.test_knowledge_base_data():
            success = False
        
        # Test search functionality
        if not self.test_search_functionality():
            success = False
        
        # Test web interface pages
        if not self.test_web_interface_pages():
            success = False
        
        # Test web to API integration
        if not self.test_web_to_api_integration():
            success = False
        
        # Print summary
        self.print_summary()
        
        return success


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test the Flask web interface")
    parser.add_argument("--web-url", default="http://localhost:5000", 
                       help="Web interface URL")
    parser.add_argument("--api-url", default="http://localhost:8000", 
                       help="API backend URL")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Verbose output")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create test runner
    runner = WebInterfaceTestRunner(web_url=args.web_url, api_url=args.api_url)
    
    # Run all tests
    success = runner.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
