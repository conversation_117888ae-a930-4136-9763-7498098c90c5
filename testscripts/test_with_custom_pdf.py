#!/usr/bin/env python3
"""
Enhanced PDF Processing Test Script

This script allows you to test the PDF processing API with any PDF file.
It can copy files to the documents directory and process them.

Usage:
    python test_with_custom_pdf.py --pdf /path/to/your/file.pdf
    python test_with_custom_pdf.py --pdf /path/to/your/file.pdf --copy
    python test_with_custom_pdf.py --pdf /path/to/your/file.pdf --name custom_name.pdf
"""

import os
import sys
import shutil
import argparse
import logging
from pathlib import Path
from typing import Optional

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import the existing API tester
from testscripts.test_api_python import APITester

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class CustomPDFTester(APITester):
    """Enhanced API tester that can handle custom PDF files."""
    
    def __init__(self, base_url: str = "http://localhost:8000", api_key: str = None):
        super().__init__(base_url, api_key)
        self.documents_dir = Path("documents")
        self.documents_dir.mkdir(exist_ok=True)
    
    def copy_pdf_to_documents(self, source_pdf: str, target_name: Optional[str] = None) -> Optional[str]:
        """Copy a PDF file to the documents directory."""
        source_path = Path(source_pdf)
        
        # Validate source file
        if not source_path.exists():
            logger.error(f"Source PDF file not found: {source_pdf}")
            return None
        
        if not source_path.suffix.lower() == '.pdf':
            logger.error(f"File is not a PDF: {source_pdf}")
            return None
        
        # Determine target filename
        if target_name:
            if not target_name.endswith('.pdf'):
                target_name += '.pdf'
            target_path = self.documents_dir / target_name
        else:
            target_path = self.documents_dir / source_path.name
        
        try:
            # Copy the file
            shutil.copy2(source_path, target_path)
            logger.info(f"✓ PDF copied to: {target_path}")
            
            # Verify the copy
            if target_path.exists() and target_path.stat().st_size > 0:
                logger.info(f"  File size: {target_path.stat().st_size:,} bytes")
                return str(target_path)
            else:
                logger.error("✗ Copy verification failed")
                return None
                
        except Exception as e:
            logger.error(f"✗ Failed to copy PDF: {e}")
            return None
    
    def validate_pdf_file(self, pdf_path: str) -> bool:
        """Validate that the PDF file is readable."""
        try:
            file_path = Path(pdf_path)
            
            if not file_path.exists():
                logger.error(f"PDF file not found: {pdf_path}")
                return False
            
            file_size = file_path.stat().st_size
            if file_size == 0:
                logger.error(f"PDF file is empty: {pdf_path}")
                return False
            
            if file_size > 50 * 1024 * 1024:  # 50MB limit
                logger.warning(f"PDF file is very large ({file_size:,} bytes). Processing may take longer.")
            
            # Try to read the first few bytes to check if it's a valid PDF
            with open(pdf_path, 'rb') as f:
                header = f.read(8)
                if not header.startswith(b'%PDF-'):
                    logger.error(f"File does not appear to be a valid PDF: {pdf_path}")
                    return False
            
            logger.info(f"✓ PDF file validated: {pdf_path}")
            logger.info(f"  Size: {file_size:,} bytes")
            return True
            
        except Exception as e:
            logger.error(f"✗ PDF validation failed: {e}")
            return False
    
    def list_documents_directory(self) -> None:
        """List all files in the documents directory."""
        logger.info("Files in documents directory:")
        
        if not self.documents_dir.exists():
            logger.info("  (documents directory does not exist)")
            return
        
        files = list(self.documents_dir.glob("*"))
        if not files:
            logger.info("  (no files found)")
            return
        
        for file_path in sorted(files):
            if file_path.is_file():
                size = file_path.stat().st_size
                logger.info(f"  {file_path.name} ({size:,} bytes)")
    
    def run_custom_pdf_test(self, pdf_path: str, copy_file: bool = False, 
                           target_name: Optional[str] = None) -> bool:
        """Run test with a custom PDF file."""
        logger.info("=" * 70)
        logger.info("Starting Custom PDF Processing Test")
        logger.info("=" * 70)
        
        # Step 1: Validate source PDF
        if not self.validate_pdf_file(pdf_path):
            return False
        
        # Step 2: Determine target path
        if copy_file:
            target_path = self.copy_pdf_to_documents(pdf_path, target_name)
            if not target_path:
                return False
        else:
            target_path = pdf_path
        
        # Step 3: List documents directory
        self.list_documents_directory()
        
        # Step 4: Run the standard test with the target PDF
        logger.info(f"Processing PDF: {target_path}")
        return self.run_full_test(target_path)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Test PDF Processing API with custom PDF files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Test with a PDF file directly
  python test_with_custom_pdf.py --pdf /path/to/document.pdf
  
  # Copy PDF to documents directory first, then test
  python test_with_custom_pdf.py --pdf /path/to/document.pdf --copy
  
  # Copy with a custom name
  python test_with_custom_pdf.py --pdf /path/to/document.pdf --copy --name my_test.pdf
  
  # Use different API server
  python test_with_custom_pdf.py --pdf document.pdf --url http://localhost:8001
        """
    )
    
    parser.add_argument("--pdf", required=True, help="Path to the PDF file to test")
    parser.add_argument("--copy", action="store_true", 
                       help="Copy the PDF to documents directory before testing")
    parser.add_argument("--name", help="Custom name for the copied PDF file")
    parser.add_argument("--url", default="http://localhost:8000", help="API base URL")
    parser.add_argument("--api-key", help="API key (uses default if not provided)")
    parser.add_argument("--list-docs", action="store_true", 
                       help="List files in documents directory and exit")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create tester
    tester = CustomPDFTester(base_url=args.url, api_key=args.api_key)
    
    # Handle list-docs command
    if args.list_docs:
        tester.list_documents_directory()
        return
    
    # Validate arguments
    if args.name and not args.copy:
        logger.error("--name can only be used with --copy")
        sys.exit(1)
    
    # Run the test
    try:
        success = tester.run_custom_pdf_test(
            pdf_path=args.pdf,
            copy_file=args.copy,
            target_name=args.name
        )
        
        if success:
            logger.info("🎉 Test completed successfully!")
            sys.exit(0)
        else:
            logger.error("❌ Test failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
