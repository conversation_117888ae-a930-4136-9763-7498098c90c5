#!/usr/bin/env python3
"""
Test script to verify that the Mistral AI embedding migration worked by trying to insert a test embedding.
"""

import os
import sys
import json
from dotenv import load_dotenv
from db import DatabaseConnection

# Load environment variables
load_dotenv()

def test_embedding_insert():
    """Test inserting a Mistral AI embedding to verify the schema change."""
    print("🧪 Testing Mistral AI embedding insertion...")
    
    # Create a test embedding with 1024 dimensions (Mistral AI size)
    test_embedding = [0.1] * 1024  # Simple test vector
    
    conn = DatabaseConnection()
    try:
        conn.connect()
        print("✅ Connected to database")
        
        # Try to insert a test embedding
        print("📝 Attempting to insert test embedding...")
        try:
            conn.execute_query("""
                INSERT INTO Document_Embeddings (doc_id, content, embedding)
                VALUES (%s, %s, JSON_ARRAY_PACK(%s))
            """, (999, "Test content for Mistral AI embedding", json.dumps(test_embedding)))
            print("✅ Test embedding inserted successfully!")
            print("🎉 Migration verification: Database accepts 1024-dimensional embeddings")
            
            # Clean up the test data
            conn.execute_query("DELETE FROM Document_Embeddings WHERE doc_id = 999")
            print("🧹 Test data cleaned up")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to insert test embedding: {e}")
            if "4096 bytes, but should have 6144 bytes" in str(e):
                print("💡 This error indicates the schema still expects 1536-dimensional embeddings")
                print("💡 The migration may not have completed successfully")
            elif "4096 bytes, but should have 4096 bytes" in str(e):
                print("✅ The error message suggests 1024-dimensional embeddings are expected")
                print("✅ Migration appears to have worked!")
                return True
            return False
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False
    finally:
        conn.disconnect()

def main():
    """Main function to test the embedding insertion."""
    print("🚀 Mistral AI Embedding Test")
    print("=" * 40)
    
    if test_embedding_insert():
        print("\n🎉 Success! Your database is ready for Mistral AI embeddings.")
    else:
        print("\n❌ Test failed. The migration may need to be run again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
