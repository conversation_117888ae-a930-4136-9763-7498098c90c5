-- Migration script to update database schema for Mistral AI embeddings
-- This changes the embedding dimension from 1536 (OpenAI) to 1024 (Mistral AI)

-- Step 1: Drop the existing vector index
DROP INDEX IF EXISTS embedding_vec_idx ON Document_Embeddings;

-- Step 2: Drop the existing embedding column
ALTER TABLE Document_Embeddings DROP COLUMN embedding;

-- Step 3: Add the new embedding column with correct dimensions for Mistral AI
ALTER TABLE Document_Embeddings ADD COLUMN embedding VECTOR(1024);

-- Step 4: Recreate the vector index with Mistral AI dimensions
ALTER TABLE Document_Embeddings 
ADD VECTOR INDEX embedding_vec_idx (embedding)
INDEX_OPTIONS '{"index_type": "HNSW_FLAT", "metric_type": "DOT_PRODUCT", "M": 32, "efConstruction": 200}';

-- Step 5: Clear any existing data that might have old embeddings
-- (Optional - uncomment if you want to clear existing data)
-- DELETE FROM Document_Embeddings;
-- DELETE FROM Chunk_Metadata;
-- DELETE FROM ProcessingStatus;
-- DELETE FROM Documents;
-- DELETE FROM Entities;
-- DELETE FROM Relationships;

-- Verify the changes
DESCRIBE Document_Embeddings;
SHOW INDEXES FROM Document_Embeddings;
