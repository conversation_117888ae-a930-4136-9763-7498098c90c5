#!/usr/bin/env python3
"""
Quick test script to check available Mistral AI models and basic functionality.
"""

import os
import sys
from dotenv import load_dotenv
from mistralai import Mistral

def test_mistral_models():
    """Test different Mistral models to see which ones work."""

    # Load environment variables
    load_dotenv(override=True)

    api_key = os.getenv('MISTRAL_API_KEY')
    if not api_key:
        print("❌ MISTRAL_API_KEY not found in environment variables")
        return

    print(f"✅ Found MISTRAL_API_KEY: {api_key[:8]}...")

    # Initialize client
    client = Mistral(api_key=api_key)

    # Models to test (from free to premium)
    models_to_test = [
        "mistral-small-2503",      # Free model - good for general tasks
        "open-mistral-nemo",       # Free model - multilingual
        "devstral-small-2505",     # Free model - for coding
        "mistral-large-2411",      # Premium model - top tier
        "mistral-medium-2505",     # Premium model - multimodal
    ]

    working_models = []

    for model in models_to_test:
        print(f"\n🧪 Testing model: {model}")

        try:
            messages = [
                {"role": "user", "content": "Hello! Please respond with just 'OK' to confirm you're working."}
            ]

            response = client.chat.complete(
                model=model,
                messages=messages,
                max_tokens=10
            )

            if hasattr(response, 'choices') and response.choices:
                content = response.choices[0].message.content.strip()
                print(f"✅ {model} works! Response: {content}")
                working_models.append(model)
            else:
                print(f"❌ {model} - No response received")

        except Exception as e:
            error_msg = str(e)
            if "model_not_found" in error_msg.lower() or "not found" in error_msg.lower():
                print(f"❌ {model} - Model not available")
            elif "quota" in error_msg.lower() or "limit" in error_msg.lower():
                print(f"⚠️  {model} - Rate limit or quota exceeded")
            elif "unauthorized" in error_msg.lower() or "authentication" in error_msg.lower():
                print(f"❌ {model} - Authentication failed")
            else:
                print(f"❌ {model} - Error: {error_msg}")

    print(f"\n{'='*50}")
    print("SUMMARY")
    print(f"{'='*50}")

    if working_models:
        print(f"✅ Working models ({len(working_models)}):")
        for model in working_models:
            print(f"   - {model}")

        print(f"\n💡 Recommended model for your config: {working_models[0]}")
        print(f"   Update config/config.yaml with: model: \"{working_models[0]}\"")
    else:
        print("❌ No working models found!")
        print("   Please check your MISTRAL_API_KEY and account status")

    return working_models

def test_json_extraction():
    """Test JSON extraction capability with the first working model."""

    load_dotenv(override=True)
    api_key = os.getenv('MISTRAL_API_KEY')
    client = Mistral(api_key=api_key)

    # Try with the model from config
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    from core.config import config

    model = config.knowledge_creation['entity_extraction']['model']
    print(f"\n🧪 Testing JSON extraction with configured model: {model}")

    try:
        messages = [
            {
                "role": "system",
                "content": "You must respond with valid JSON only. Extract entities from the given text."
            },
            {
                "role": "user",
                "content": """Extract entities from this text: "Apple Inc. is a technology company founded by Steve Jobs in Cupertino, California."

Return JSON in this format:
{
  "entities": [
    {"name": "entity_name", "type": "ORGANIZATION|PERSON|LOCATION", "description": "brief description"}
  ]
}"""
            }
        ]

        response = client.chat.complete(
            model=model,
            messages=messages,
            response_format={"type": "json_object"}
        )

        if hasattr(response, 'choices') and response.choices:
            content = response.choices[0].message.content.strip()
            print(f"✅ JSON Response received:")
            print(content)

            # Try to parse JSON
            import json
            try:
                parsed = json.loads(content)
                print(f"✅ JSON is valid! Found {len(parsed.get('entities', []))} entities")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ Invalid JSON: {e}")
                return False
        else:
            print(f"❌ No response received")
            return False

    except Exception as e:
        print(f"❌ JSON extraction test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Mistral AI Models and Configuration")
    print("=" * 50)

    # Test model availability
    working_models = test_mistral_models()

    if working_models:
        # Test JSON extraction
        test_json_extraction()

    print(f"\n🏁 Testing complete!")
