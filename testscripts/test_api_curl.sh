#!/bin/bash

# API Testing Script using curl
# Tests the PDF upload and processing endpoints

set -e  # Exit on any error

# Configuration
API_BASE_URL="http://localhost:8000"
API_KEY="a614192a822e6daef18a68029396879632c768dac57fe826043cf785bcdf519a7"
TEST_PDF="documents/sample_test_document.pdf"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if server is running
check_server() {
    print_status "Checking if API server is running..."
    if curl -s -f "${API_BASE_URL}/docs" > /dev/null; then
        print_success "API server is running"
        return 0
    else
        print_error "API server is not running at ${API_BASE_URL}"
        print_status "Please start the server with: python3 main.py"
        return 1
    fi
}

# Function to check if Redis is running
check_redis() {
    print_status "Checking if Redis is running..."
    if command -v redis-cli &> /dev/null; then
        if redis-cli ping > /dev/null 2>&1; then
            print_success "Redis is running"
            return 0
        else
            print_warning "Redis is not responding"
        fi
    else
        # Try to connect to Redis port using nc or telnet
        if command -v nc &> /dev/null; then
            if nc -z localhost 6379 2>/dev/null; then
                print_success "Redis port is open"
                return 0
            fi
        elif command -v telnet &> /dev/null; then
            if timeout 2 telnet localhost 6379 </dev/null >/dev/null 2>&1; then
                print_success "Redis port is open"
                return 0
            fi
        fi
    fi

    print_error "Redis is not running on localhost:6379"
    print_status "Redis is required for PDF processing. Start it with:"
    print_status "  macOS: brew install redis && brew services start redis"
    print_status "  Ubuntu: sudo apt-get install redis-server && sudo systemctl start redis-server"
    print_status "  Docker: docker run -d -p 6379:6379 redis:alpine"
    print_status "Or run: python3 setup_redis.py"
    return 1
}

# Function to create a sample PDF if it doesn't exist
create_sample_pdf() {
    if [ ! -f "$TEST_PDF" ]; then
        print_status "Creating sample PDF for testing..."
        mkdir -p documents

        # Create a simple text file first
        cat > documents/sample_test_document.txt << 'EOF'
# Sample Test Document

## Introduction

This is a sample document created for testing the PDF processing API.
It contains multiple sections that will be processed into chunks.

## Section 1: Overview

This section provides an overview of the document processing system.
The system can handle PDF files and extract meaningful chunks of text
for embedding generation and knowledge graph creation.

## Section 2: Features

Key features of the system include:
- PDF text extraction
- Semantic chunking
- Embedding generation
- Knowledge graph construction
- Real-time processing status

## Section 3: Technical Details

The system uses advanced natural language processing techniques
to analyze documents and extract structured information.
This enables powerful search and retrieval capabilities.

## Conclusion

This document demonstrates the capabilities of the document
processing system and serves as a test case for API validation.
EOF

        # Try to convert to PDF using available tools
        if command -v pandoc &> /dev/null; then
            print_status "Converting to PDF using pandoc..."
            pandoc documents/sample_test_document.txt -o "$TEST_PDF"
            print_success "Sample PDF created: $TEST_PDF"
        elif command -v wkhtmltopdf &> /dev/null; then
            print_status "Converting to PDF using wkhtmltopdf..."
            echo "<pre>$(cat documents/sample_test_document.txt)</pre>" | wkhtmltopdf - "$TEST_PDF"
            print_success "Sample PDF created: $TEST_PDF"
        else
            print_warning "No PDF conversion tool found (pandoc, wkhtmltopdf)"
            print_warning "Please manually create a PDF file at: $TEST_PDF"
            print_warning "Or install pandoc: brew install pandoc (macOS) or apt-get install pandoc (Ubuntu)"
            return 1
        fi
    else
        print_success "Sample PDF already exists: $TEST_PDF"
    fi
    return 0
}

# Function to upload PDF
upload_pdf() {
    print_status "Uploading PDF file: $TEST_PDF"

    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "X-API-Key: $API_KEY" \
        -F "file=@$TEST_PDF" \
        "${API_BASE_URL}/upload-pdf")

    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    # Extract all but the last line (more compatible than 'head -n -1')
    response_body=$(echo "$response" | sed '$d')

    if [ "$http_code" = "202" ]; then
        print_success "PDF uploaded successfully"
        echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"

        # Extract task_id and doc_id for further testing
        TASK_ID=$(echo "$response_body" | jq -r '.task_id' 2>/dev/null || echo "")
        DOC_ID=$(echo "$response_body" | jq -r '.doc_id' 2>/dev/null || echo "")

        if [ -n "$TASK_ID" ] && [ "$TASK_ID" != "null" ]; then
            print_success "Task ID: $TASK_ID"
        fi
        if [ -n "$DOC_ID" ] && [ "$DOC_ID" != "null" ]; then
            print_success "Document ID: $DOC_ID"
        fi

        return 0
    else
        print_error "Upload failed with HTTP code: $http_code"
        echo "$response_body"
        return 1
    fi
}

# Function to check task status
check_task_status() {
    if [ -z "$TASK_ID" ]; then
        print_warning "No task ID available for status check"
        return 1
    fi

    print_status "Checking task status: $TASK_ID"

    response=$(curl -s -w "\n%{http_code}" \
        -H "X-API-Key: $API_KEY" \
        "${API_BASE_URL}/task-status/$TASK_ID")

    http_code=$(echo "$response" | tail -n1)
    # Extract all but the last line (more compatible than 'head -n -1')
    response_body=$(echo "$response" | sed '$d')

    if [ "$http_code" = "200" ]; then
        print_success "Task status retrieved"
        echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"

        # Extract status
        status=$(echo "$response_body" | jq -r '.status' 2>/dev/null || echo "")
        if [ "$status" = "SUCCESS" ]; then
            print_success "Task completed successfully!"
        elif [ "$status" = "FAILURE" ]; then
            print_error "Task failed!"
        elif [ "$status" = "PENDING" ]; then
            print_warning "Task is still pending..."
        fi

        return 0
    else
        print_error "Failed to get task status with HTTP code: $http_code"
        echo "$response_body"
        return 1
    fi
}

# Function to check processing status
check_processing_status() {
    if [ -z "$DOC_ID" ]; then
        print_warning "No document ID available for status check"
        return 1
    fi

    print_status "Checking processing status for document: $DOC_ID"

    response=$(curl -s -w "\n%{http_code}" \
        -H "X-API-Key: $API_KEY" \
        "${API_BASE_URL}/processing-status/$DOC_ID")

    http_code=$(echo "$response" | tail -n1)
    # Extract all but the last line (more compatible than 'head -n -1')
    response_body=$(echo "$response" | sed '$d')

    if [ "$http_code" = "200" ]; then
        print_success "Processing status retrieved"
        echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
        return 0
    else
        print_error "Failed to get processing status with HTTP code: $http_code"
        echo "$response_body"
        return 1
    fi
}

# Function to get document chunks
get_document_chunks() {
    if [ -z "$DOC_ID" ]; then
        print_warning "No document ID available for chunks retrieval"
        return 1
    fi

    print_status "Retrieving document chunks for document: $DOC_ID"

    response=$(curl -s -w "\n%{http_code}" \
        -H "X-API-Key: $API_KEY" \
        "${API_BASE_URL}/doc-chunks?doc_id=$DOC_ID")

    http_code=$(echo "$response" | tail -n1)
    # Extract all but the last line (more compatible than 'head -n -1')
    response_body=$(echo "$response" | sed '$d')

    if [ "$http_code" = "200" ]; then
        print_success "Document chunks retrieved"
        # Pretty print with limited output
        chunk_count=$(echo "$response_body" | jq '.chunks | length' 2>/dev/null || echo "unknown")
        print_success "Found $chunk_count chunks"

        # Show first chunk preview
        first_chunk=$(echo "$response_body" | jq -r '.chunks[0].content' 2>/dev/null | head -c 200)
        if [ -n "$first_chunk" ] && [ "$first_chunk" != "null" ]; then
            print_status "First chunk preview:"
            echo "$first_chunk..."
        fi

        return 0
    else
        print_error "Failed to get document chunks with HTTP code: $http_code"
        echo "$response_body"
        return 1
    fi
}

# Function to test search
test_search() {
    print_status "Testing search functionality..."

    search_query="document processing"

    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -d "{\"query\": \"$search_query\", \"top_k\": 3}" \
        "${API_BASE_URL}/kag-search")

    http_code=$(echo "$response" | tail -n1)
    # Extract all but the last line (more compatible than 'head -n -1')
    response_body=$(echo "$response" | sed '$d')

    if [ "$http_code" = "200" ]; then
        print_success "Search completed successfully"

        # Extract key information
        result_count=$(echo "$response_body" | jq '.results | length' 2>/dev/null || echo "unknown")
        execution_time=$(echo "$response_body" | jq -r '.execution_time' 2>/dev/null || echo "unknown")

        print_success "Found $result_count results in ${execution_time}s"

        # Show generated response
        generated_response=$(echo "$response_body" | jq -r '.generated_response' 2>/dev/null)
        if [ -n "$generated_response" ] && [ "$generated_response" != "null" ]; then
            print_status "Generated response preview:"
            echo "$generated_response" | head -c 300
            echo "..."
        fi

        return 0
    else
        print_error "Search failed with HTTP code: $http_code"
        echo "$response_body"
        return 1
    fi
}

# Function to wait for processing completion
wait_for_completion() {
    if [ -z "$TASK_ID" ]; then
        print_warning "No task ID available for monitoring"
        return 1
    fi

    print_status "Waiting for processing to complete..."
    max_attempts=6  # Reduced from 30 to make tests faster
    attempt=0

    while [ $attempt -lt $max_attempts ]; do
        sleep 2  # Reduced from 5 to make tests faster
        attempt=$((attempt + 1))
        print_status "Checking status attempt $attempt/$max_attempts..."

        response=$(curl -s \
            -H "X-API-Key: $API_KEY" \
            "${API_BASE_URL}/task-status/$TASK_ID")

        status=$(echo "$response" | jq -r '.status' 2>/dev/null || echo "")

        case "$status" in
            "SUCCESS")
                print_success "Processing completed successfully!"
                return 0
                ;;
            "FAILURE")
                print_error "Processing failed!"
                echo "$response" | jq '.' 2>/dev/null || echo "$response"
                return 1
                ;;
            "PENDING"|"PROGRESS")
                print_status "Still processing... (attempt $attempt/$max_attempts)"
                ;;
            *)
                print_warning "Unknown status: $status"
                ;;
        esac
    done

    print_warning "Timeout waiting for processing completion"
    return 1
}

# Main execution
main() {
    echo "=================================="
    echo "PDF Processing API Test Script"
    echo "=================================="

    # Check if jq is available for JSON parsing
    if ! command -v jq &> /dev/null; then
        print_warning "jq not found. JSON output will not be formatted."
        print_status "Install jq for better output: brew install jq (macOS) or apt-get install jq (Ubuntu)"
    fi

    # Run tests
    check_server || exit 1

    # Check Redis (warn but don't exit)
    if ! check_redis; then
        print_warning "Redis check failed. PDF upload may fail."
        print_status "Continuing with tests anyway..."
    fi

    create_sample_pdf || exit 1
    upload_pdf || exit 1

    # Wait a moment for processing to start
    sleep 2

    check_task_status
    check_processing_status

    # Wait for completion
    if wait_for_completion; then
        print_status "Processing completed. Running additional tests..."

        # Test additional endpoints
        get_document_chunks
        test_search

        print_success "All tests completed successfully!"
    else
        print_warning "Processing did not complete within timeout, but upload was successful"
    fi

    echo "=================================="
    echo "Test Summary:"
    echo "- API Server: ✓"
    echo "- PDF Upload: ✓"
    echo "- Task Monitoring: ✓"
    if [ -n "$DOC_ID" ]; then
        echo "- Document ID: $DOC_ID"
    fi
    if [ -n "$TASK_ID" ]; then
        echo "- Task ID: $TASK_ID"
    fi
    echo "=================================="
}

# Run main function
main "$@"
