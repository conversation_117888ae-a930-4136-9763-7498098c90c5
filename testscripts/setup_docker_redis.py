#!/usr/bin/env python3
"""
Docker Redis Setup Script

This script helps set up Redis using Docker Compose for the PDF processing API.
It provides multiple Docker Compose configurations for different use cases.
"""

import os
import sys
import subprocess
import time
import logging
import argparse
import socket

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def check_command_available(command):
    """Check if a command is available in PATH."""
    try:
        subprocess.run([command, '--version'], 
                      capture_output=True, check=False)
        return True
    except FileNotFoundError:
        return False


def check_redis_running(host='localhost', port=6379, timeout=5):
    """Check if Redis is running."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception:
        return False


def check_docker():
    """Check if Docker and Docker Compose are available."""
    logger.info("Checking Docker availability...")
    
    if not check_command_available('docker'):
        logger.error("Docker not found. Please install Docker first:")
        logger.error("  https://docs.docker.com/get-docker/")
        return False
    
    # Check if Docker daemon is running
    try:
        result = subprocess.run(['docker', 'info'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            logger.error("Docker daemon is not running. Please start Docker.")
            return False
    except Exception as e:
        logger.error(f"Error checking Docker: {e}")
        return False
    
    # Check Docker Compose
    compose_cmd = None
    if check_command_available('docker-compose'):
        compose_cmd = 'docker-compose'
    else:
        # Try docker compose (newer syntax)
        try:
            result = subprocess.run(['docker', 'compose', 'version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                compose_cmd = 'docker compose'
        except Exception:
            pass
    
    if not compose_cmd:
        logger.error("Docker Compose not found. Please install Docker Compose:")
        logger.error("  https://docs.docker.com/compose/install/")
        return False
    
    logger.info(f"✓ Docker and Docker Compose available (using: {compose_cmd})")
    return compose_cmd


def start_redis_only(compose_cmd):
    """Start Redis-only using docker-compose.redis-only.yml"""
    logger.info("Starting Redis-only container...")
    
    compose_file = "docker-compose.redis-only.yml"
    if not os.path.exists(compose_file):
        logger.error(f"Compose file not found: {compose_file}")
        return False
    
    try:
        # Stop any existing containers
        logger.info("Stopping any existing Redis containers...")
        if compose_cmd == 'docker-compose':
            subprocess.run(['docker-compose', '-f', compose_file, 'down'], 
                         capture_output=True)
        else:
            subprocess.run(['docker', 'compose', '-f', compose_file, 'down'], 
                         capture_output=True)
        
        # Start Redis
        logger.info("Starting Redis container...")
        if compose_cmd == 'docker-compose':
            result = subprocess.run(['docker-compose', '-f', compose_file, 'up', '-d'], 
                                  capture_output=True, text=True)
        else:
            result = subprocess.run(['docker', 'compose', '-f', compose_file, 'up', '-d'], 
                                  capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Failed to start Redis: {result.stderr}")
            return False
        
        # Wait for Redis to be ready
        logger.info("Waiting for Redis to be ready...")
        for i in range(30):
            if check_redis_running():
                logger.info("✓ Redis is now running")
                return True
            time.sleep(1)
        
        logger.error("Redis failed to start within timeout")
        return False
        
    except Exception as e:
        logger.error(f"Error starting Redis: {e}")
        return False


def start_dev_environment(compose_cmd):
    """Start development environment with Redis and monitoring tools"""
    logger.info("Starting development environment...")
    
    compose_file = "docker-compose.dev.yml"
    if not os.path.exists(compose_file):
        logger.error(f"Compose file not found: {compose_file}")
        return False
    
    try:
        # Stop any existing containers
        logger.info("Stopping any existing containers...")
        if compose_cmd == 'docker-compose':
            subprocess.run(['docker-compose', '-f', compose_file, 'down'], 
                         capture_output=True)
        else:
            subprocess.run(['docker', 'compose', '-f', compose_file, 'down'], 
                         capture_output=True)
        
        # Start services
        logger.info("Starting development environment...")
        if compose_cmd == 'docker-compose':
            result = subprocess.run(['docker-compose', '-f', compose_file, 'up', '-d'], 
                                  capture_output=True, text=True)
        else:
            result = subprocess.run(['docker', 'compose', '-f', compose_file, 'up', '-d'], 
                                  capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Failed to start development environment: {result.stderr}")
            return False
        
        # Wait for Redis to be ready
        logger.info("Waiting for services to be ready...")
        for i in range(30):
            if check_redis_running():
                logger.info("✓ Redis is now running")
                logger.info("✓ Development environment started")
                logger.info("  Redis: localhost:6379")
                logger.info("  Redis Commander: http://localhost:8081 (admin/admin)")
                logger.info("  RedisInsight: http://localhost:8001")
                return True
            time.sleep(1)
        
        logger.error("Services failed to start within timeout")
        return False
        
    except Exception as e:
        logger.error(f"Error starting development environment: {e}")
        return False


def start_full_stack(compose_cmd):
    """Start full stack using main docker-compose.yml"""
    logger.info("Starting full application stack...")
    
    compose_file = "docker-compose.yml"
    if not os.path.exists(compose_file):
        logger.error(f"Compose file not found: {compose_file}")
        return False
    
    try:
        # Check if .env file exists
        if not os.path.exists('.env'):
            logger.warning(".env file not found. Some services may fail to start.")
        
        # Stop any existing containers
        logger.info("Stopping any existing containers...")
        if compose_cmd == 'docker-compose':
            subprocess.run(['docker-compose', 'down'], capture_output=True)
        else:
            subprocess.run(['docker', 'compose', 'down'], capture_output=True)
        
        # Start services
        logger.info("Starting full application stack...")
        if compose_cmd == 'docker-compose':
            result = subprocess.run(['docker-compose', 'up', '-d'], 
                                  capture_output=True, text=True)
        else:
            result = subprocess.run(['docker', 'compose', 'up', '-d'], 
                                  capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Failed to start full stack: {result.stderr}")
            return False
        
        # Wait for services to be ready
        logger.info("Waiting for services to be ready...")
        for i in range(60):
            if check_redis_running():
                logger.info("✓ Full application stack started")
                logger.info("  API: http://localhost:8000")
                logger.info("  Redis: localhost:6379")
                return True
            time.sleep(1)
        
        logger.error("Services failed to start within timeout")
        return False
        
    except Exception as e:
        logger.error(f"Error starting full stack: {e}")
        return False


def stop_services(compose_cmd, compose_file=None):
    """Stop Docker Compose services"""
    logger.info("Stopping Docker services...")
    
    files_to_try = []
    if compose_file:
        files_to_try.append(compose_file)
    else:
        files_to_try = [
            "docker-compose.redis-only.yml",
            "docker-compose.dev.yml", 
            "docker-compose.yml"
        ]
    
    for file in files_to_try:
        if os.path.exists(file):
            try:
                if compose_cmd == 'docker-compose':
                    if file == "docker-compose.yml":
                        subprocess.run(['docker-compose', 'down'], capture_output=True)
                    else:
                        subprocess.run(['docker-compose', '-f', file, 'down'], capture_output=True)
                else:
                    if file == "docker-compose.yml":
                        subprocess.run(['docker', 'compose', 'down'], capture_output=True)
                    else:
                        subprocess.run(['docker', 'compose', '-f', file, 'down'], capture_output=True)
                logger.info(f"✓ Stopped services from {file}")
            except Exception as e:
                logger.warning(f"Error stopping {file}: {e}")


def show_status():
    """Show status of Docker containers"""
    logger.info("Docker container status:")
    try:
        result = subprocess.run(['docker', 'ps', '--filter', 'name=kag', 
                               '--format', 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'], 
                              capture_output=True, text=True)
        if result.stdout.strip():
            print(result.stdout)
        else:
            logger.info("No KAG-related containers running")
    except Exception as e:
        logger.error(f"Error checking container status: {e}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Set up Redis using Docker Compose")
    parser.add_argument("--mode", choices=["redis-only", "dev", "full"], 
                       default="redis-only",
                       help="Setup mode (default: redis-only)")
    parser.add_argument("--stop", action="store_true", 
                       help="Stop running services")
    parser.add_argument("--status", action="store_true", 
                       help="Show container status")
    parser.add_argument("--check-only", action="store_true", 
                       help="Only check if Redis is running")
    
    args = parser.parse_args()
    
    logger.info("=" * 60)
    logger.info("Docker Redis Setup")
    logger.info("=" * 60)
    
    # Check current Redis status
    if check_redis_running():
        logger.info("✓ Redis is already running on localhost:6379")
        if args.check_only:
            sys.exit(0)
    else:
        logger.info("✗ Redis is not running on localhost:6379")
        if args.check_only:
            sys.exit(1)
    
    if args.status:
        show_status()
        sys.exit(0)
    
    # Check Docker
    compose_cmd = check_docker()
    if not compose_cmd:
        sys.exit(1)
    
    if args.stop:
        stop_services(compose_cmd)
        sys.exit(0)
    
    # Start services based on mode
    success = False
    if args.mode == "redis-only":
        success = start_redis_only(compose_cmd)
    elif args.mode == "dev":
        success = start_dev_environment(compose_cmd)
    elif args.mode == "full":
        success = start_full_stack(compose_cmd)
    
    if success:
        logger.info("=" * 60)
        logger.info("✓ Setup completed successfully!")
        logger.info("Redis is now available at localhost:6379")
        logger.info("You can now run the PDF processing API tests.")
        logger.info("")
        logger.info("To stop services:")
        logger.info("  python3 setup_docker_redis.py --stop")
        logger.info("=" * 60)
        sys.exit(0)
    else:
        logger.error("=" * 60)
        logger.error("✗ Setup failed!")
        logger.error("Check the logs above for details.")
        logger.error("=" * 60)
        sys.exit(1)


if __name__ == "__main__":
    main()
