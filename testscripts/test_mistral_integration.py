#!/usr/bin/env python3
"""
Test script to verify Mistral AI integration is working correctly.
This tests the core functionality without requiring <PERSON><PERSON>.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_mistral_api_key():
    """Test if Mistral API key is available."""
    api_key = os.getenv('MISTRAL_API_KEY')
    if not api_key:
        print("❌ MISTRAL_API_KEY not found in environment")
        return False
    print(f"✅ MISTRAL_API_KEY found: {api_key[:8]}...")
    return True

def test_mistral_client():
    """Test if Mistral client can be initialized."""
    try:
        from mistralai import Mistral
        client = Mistral(api_key=os.getenv('MISTRAL_API_KEY'))
        print("✅ Mistral client initialized successfully")
        return True, client
    except Exception as e:
        print(f"❌ Failed to initialize Mistral client: {e}")
        return False, None

def test_mistral_embeddings(client):
    """Test Mistral embeddings functionality."""
    try:
        response = client.embeddings.create(
            model="mistral-embed",
            inputs=["Hello, this is a test sentence for embeddings."]
        )

        if response and hasattr(response, 'data') and len(response.data) > 0:
            embedding = response.data[0].embedding
            print(f"✅ Embeddings working - dimension: {len(embedding)}")
            return True
        else:
            print("❌ Embeddings response is empty or malformed")
            return False

    except Exception as e:
        print(f"❌ Embeddings test failed: {e}")
        return False

def test_mistral_chat(client):
    """Test Mistral chat completion functionality."""
    try:
        response = client.chat.complete(
            model="mistral-small",
            messages=[
                {"role": "user", "content": "Hello! Please respond with just 'Test successful' if you can read this."}
            ]
        )

        if response and hasattr(response, 'choices') and len(response.choices) > 0:
            content = response.choices[0].message.content
            print(f"✅ Chat completion working - response: {content[:50]}...")
            return True
        else:
            print("❌ Chat completion response is empty or malformed")
            return False

    except Exception as e:
        print(f"❌ Chat completion test failed: {e}")
        return False

def test_pdf_processor_import():
    """Test if our PDF processor functions can be imported."""
    try:
        from processors.pdf import process_pdf, get_semantic_chunks, llamaparse_pdf
        print("✅ PDF processor functions import successful")
        return True
    except Exception as e:
        print(f"❌ PDF processor functions import failed: {e}")
        return False

def test_knowledge_extractor_import():
    """Test if our knowledge extractor can be imported."""
    try:
        from processors.knowledge import KnowledgeGraphGenerator
        print("✅ KnowledgeGraphGenerator import successful")
        return True
    except Exception as e:
        print(f"❌ KnowledgeGraphGenerator import failed: {e}")
        return False

def main():
    """Run all integration tests."""
    print("🧪 Running Mistral AI Integration Tests")
    print("=" * 50)

    tests_passed = 0
    total_tests = 0

    # Test 1: API Key
    total_tests += 1
    if test_mistral_api_key():
        tests_passed += 1

    # Test 2: Client initialization
    total_tests += 1
    client_ok, client = test_mistral_client()
    if client_ok:
        tests_passed += 1

    if client:
        # Test 3: Embeddings
        total_tests += 1
        if test_mistral_embeddings(client):
            tests_passed += 1

        # Test 4: Chat completion
        total_tests += 1
        if test_mistral_chat(client):
            tests_passed += 1

    # Test 5: PDF Processor import
    total_tests += 1
    if test_pdf_processor_import():
        tests_passed += 1

    # Test 6: Knowledge Extractor import
    total_tests += 1
    if test_knowledge_extractor_import():
        tests_passed += 1

    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")

    if tests_passed == total_tests:
        print("🎉 All tests passed! Mistral integration is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
