#!/usr/bin/env python3
"""
Test script for Mistral AI embeddings integration.

This script tests the Mistral embedding functionality to ensure it works
correctly after replacing OpenAI embeddings.

Usage:
    python test_mistral_embeddings.py
    python test_mistral_embeddings.py --text "Custom text to embed"
    python test_mistral_embeddings.py --compare  # Compare with OpenAI if available
"""

import os
import sys
import argparse
import logging
import json
from typing import List, Optional, Dict, Any
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from mistralai import Mistral
from core.config import config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class MistralEmbeddingTester:
    """Test class for Mistral AI embeddings."""

    def __init__(self):
        """Initialize the tester."""
        load_dotenv(override=True)

        self.mistral_api_key = os.getenv('MISTRAL_API_KEY')
        if not self.mistral_api_key:
            raise ValueError("MISTRAL_API_KEY not found in environment variables")

        self.mistral_client = Mistral(api_key=self.mistral_api_key)

        # Get embedding configuration
        self.embedding_config = config.knowledge_creation.get('embeddings', {})
        self.embedding_model = self.embedding_config.get('model', 'mistral-embed')

        logger.info(f"Using Mistral embedding model: {self.embedding_model}")

    def test_single_embedding(self, text: str) -> Optional[List[float]]:
        """Test embedding generation for a single text."""
        logger.info(f"Testing embedding for text: '{text[:100]}...'")

        try:
            response = self.mistral_client.embeddings.create(
                model=self.embedding_model,
                inputs=[text]
            )

            if hasattr(response, 'data') and response.data:
                embedding = response.data[0].embedding
                logger.info(f"✅ Embedding generated successfully")
                logger.info(f"   Embedding dimensions: {len(embedding)}")
                logger.info(f"   First 5 values: {embedding[:5]}")
                return embedding
            else:
                logger.error("❌ No embedding data in response")
                return None

        except Exception as e:
            logger.error(f"❌ Embedding generation failed: {str(e)}")
            return None

    def test_batch_embeddings(self, texts: List[str]) -> Optional[List[List[float]]]:
        """Test embedding generation for multiple texts."""
        logger.info(f"Testing batch embedding for {len(texts)} texts")

        try:
            response = self.mistral_client.embeddings.create(
                model=self.embedding_model,
                inputs=texts
            )

            if hasattr(response, 'data') and response.data:
                embeddings = [item.embedding for item in response.data]
                logger.info(f"✅ Batch embeddings generated successfully")
                logger.info(f"   Number of embeddings: {len(embeddings)}")
                logger.info(f"   Embedding dimensions: {len(embeddings[0]) if embeddings else 0}")
                return embeddings
            else:
                logger.error("❌ No embedding data in response")
                return None

        except Exception as e:
            logger.error(f"❌ Batch embedding generation failed: {str(e)}")
            return None

    def test_similarity(self, text1: str, text2: str) -> Optional[float]:
        """Test similarity calculation between two texts."""
        logger.info("Testing embedding similarity calculation")

        # Get embeddings for both texts
        embeddings = self.test_batch_embeddings([text1, text2])
        if not embeddings or len(embeddings) != 2:
            logger.error("❌ Failed to get embeddings for similarity test")
            return None

        # Calculate cosine similarity
        import numpy as np

        vec1 = np.array(embeddings[0])
        vec2 = np.array(embeddings[1])

        # Cosine similarity
        similarity = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))

        logger.info(f"✅ Similarity calculated: {similarity:.4f}")
        logger.info(f"   Text 1: '{text1[:50]}...'")
        logger.info(f"   Text 2: '{text2[:50]}...'")

        return float(similarity)

    def test_pdf_processor_integration(self) -> bool:
        """Test integration with the PDF processor configuration."""
        logger.info("Testing PDF processor integration")

        try:
            # Import the PDF processor
            from processors.pdf import process_pdf

            # Check if the configuration is loaded correctly
            embedding_config = config.knowledge_creation.get('embeddings', {})
            if not embedding_config:
                logger.error("❌ No embedding configuration found")
                return False

            logger.info(f"✅ Embedding configuration loaded:")
            logger.info(f"   Model: {embedding_config.get('model', 'not set')}")
            logger.info(f"   Provider: {embedding_config.get('provider', 'not set')}")

            return True

        except Exception as e:
            logger.error(f"❌ PDF processor integration test failed: {str(e)}")
            return False

    def compare_with_openai(self, text: str) -> bool:
        """Compare Mistral embeddings with OpenAI if available."""
        logger.info("Comparing Mistral embeddings with OpenAI")

        # Get Mistral embedding
        mistral_embedding = self.test_single_embedding(text)
        if not mistral_embedding:
            logger.error("❌ Failed to get Mistral embedding for comparison")
            return False

        # Try to get OpenAI embedding
        try:
            from openai import OpenAI
            openai_api_key = os.getenv('OPENAI_API_KEY')

            if not openai_api_key:
                logger.warning("⚠️  OPENAI_API_KEY not available, skipping comparison")
                return True

            openai_client = OpenAI(api_key=openai_api_key)
            openai_response = openai_client.embeddings.create(
                model="text-embedding-ada-002",
                input=text
            )
            openai_embedding = openai_response.data[0].embedding

            logger.info("✅ Comparison results:")
            logger.info(f"   Mistral dimensions: {len(mistral_embedding)}")
            logger.info(f"   OpenAI dimensions: {len(openai_embedding)}")

            # Note: Different models will have different dimensions and values
            # This is just to show both work
            logger.info("   Both embedding services are working correctly")
            logger.info("   (Note: Different models produce different embeddings)")

            return True

        except ImportError:
            logger.warning("⚠️  OpenAI library not available, skipping comparison")
            return True
        except Exception as e:
            logger.error(f"❌ OpenAI embedding comparison failed: {str(e)}")
            return False

    def run_comprehensive_test(self, custom_text: Optional[str] = None,
                             compare_openai: bool = False) -> bool:
        """Run comprehensive embedding tests."""
        logger.info("=" * 60)
        logger.info("MISTRAL AI EMBEDDINGS TEST SUITE")
        logger.info("=" * 60)

        # Test texts
        test_texts = [
            custom_text or "This is a sample document about artificial intelligence and machine learning.",
            "Docker is a containerization platform for developers.",
            "Python is a programming language used for data science.",
            "The quick brown fox jumps over the lazy dog."
        ]

        similar_texts = [
            "Machine learning is a subset of artificial intelligence.",
            "AI and ML are important technologies in modern computing."
        ]

        success_count = 0
        total_tests = 0

        # Test 1: Single embedding
        logger.info("Test 1: Single text embedding")
        total_tests += 1
        if self.test_single_embedding(test_texts[0]):
            success_count += 1

        # Test 2: Batch embeddings
        logger.info("\nTest 2: Batch text embeddings")
        total_tests += 1
        if self.test_batch_embeddings(test_texts):
            success_count += 1

        # Test 3: Similarity calculation
        logger.info("\nTest 3: Similarity calculation")
        total_tests += 1
        if self.test_similarity(similar_texts[0], similar_texts[1]):
            success_count += 1

        # Test 4: PDF processor integration
        logger.info("\nTest 4: PDF processor integration")
        total_tests += 1
        if self.test_pdf_processor_integration():
            success_count += 1

        # Test 5: OpenAI comparison (optional)
        if compare_openai:
            logger.info("\nTest 5: OpenAI comparison")
            total_tests += 1
            if self.compare_with_openai(test_texts[0]):
                success_count += 1

        # Summary
        logger.info("=" * 60)
        logger.info("TEST RESULTS SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Tests passed: {success_count}/{total_tests}")
        logger.info(f"Success rate: {(success_count/total_tests)*100:.1f}%")

        if success_count == total_tests:
            logger.info("🎉 ALL TESTS PASSED! Mistral embeddings are working correctly.")
        else:
            logger.warning("⚠️  Some tests failed. Please check the errors above.")

        return success_count == total_tests


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test Mistral AI embeddings integration")
    parser.add_argument("--text", help="Custom text to test embedding generation")
    parser.add_argument("--compare", action="store_true", help="Compare with OpenAI embeddings")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        tester = MistralEmbeddingTester()
        success = tester.run_comprehensive_test(
            custom_text=args.text,
            compare_openai=args.compare
        )

        sys.exit(0 if success else 1)

    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
