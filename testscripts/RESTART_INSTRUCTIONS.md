# 🔄 Restart Instructions - Fix OpenAI Error

## 🚨 **Issue Identified**

You're seeing this error:
```
[2025-06-08 11:39:09,559: INFO/ForkPoolWorker-13] HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
[2025-06-08 11:39:09,585: ERROR/ForkPoolWorker-13] Error processing PDF: Error code: 401
```

**Root Cause**: Your Docker containers are running the **old code** that still uses OpenAI, even though the source code has been updated to use Mistral AI.

## ✅ **Verification Complete**

Our verification script confirms:
- ✅ Source code is correctly using Mistral AI
- ✅ All imports are pointing to the right files
- ✅ No OpenAI calls in the current codebase
- ✅ Old PDF processor files have been removed

## 🔧 **Solution: Restart Docker Containers**

### **Option 1: Full Restart (Recommended)**

```bash
# Stop all containers
docker-compose down

# Rebuild and start with new code
docker-compose up --build -d

# Check logs to verify Mistral AI is being used
docker-compose logs -f celery
```

### **Option 2: Restart Just the Worker**

```bash
# Restart only the Celery worker
docker-compose restart celery

# Check logs
docker-compose logs -f celery
```

### **Option 3: Force Rebuild**

```bash
# Stop and remove containers
docker-compose down --volumes --remove-orphans

# Remove old images
docker-compose build --no-cache

# Start fresh
docker-compose up -d

# Monitor logs
docker-compose logs -f
```

## 🔍 **Verify the Fix**

After restarting, you should see logs like:

**✅ GOOD (Mistral AI):**
```
[INFO] Sending request to Mistral AI for semantic chunking
[INFO] HTTP Request: POST https://api.mistral.ai/v1/chat/completions "HTTP/1.1 200 OK"
[INFO] HTTP Request: POST https://api.mistral.ai/v1/embeddings "HTTP/1.1 200 OK"
```

**❌ BAD (OpenAI - should not appear):**
```
[INFO] HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 401 Unauthorized"
```

## 🐛 **If Still Seeing OpenAI Errors**

### **Check Environment Variables**

Make sure your `.env` file has:
```bash
MISTRAL_API_KEY=your_mistral_key_here
# OPENAI_API_KEY can be removed or left empty
```

### **Check Docker Environment**

Verify the containers are using the right environment:
```bash
# Check environment variables in container
docker-compose exec backend env | grep MISTRAL
docker-compose exec celery env | grep MISTRAL
```

### **Check Container Logs**

```bash
# Check backend logs
docker-compose logs backend | grep -i "mistral\|openai"

# Check celery logs  
docker-compose logs celery | grep -i "mistral\|openai"
```

### **Force Container Recreation**

```bash
# Nuclear option - completely recreate everything
docker-compose down --volumes --remove-orphans
docker system prune -f
docker-compose up --build --force-recreate -d
```

## 📋 **Expected Behavior After Fix**

1. **No OpenAI API calls** in the logs
2. **Mistral AI API calls** for:
   - Semantic chunking: `https://api.mistral.ai/v1/chat/completions`
   - Embeddings: `https://api.mistral.ai/v1/embeddings`
3. **No Google API errors** for semantic chunking
4. **Successful PDF processing** without authentication errors

## 🎯 **Quick Test**

After restarting, upload a PDF and check the logs:

```bash
# Watch logs in real-time
docker-compose logs -f celery | grep -E "(HTTP Request|Error|Mistral|OpenAI)"
```

You should see **only Mistral API calls** and **no OpenAI errors**.

## 🆘 **If Problems Persist**

1. **Check the source code** in the container:
   ```bash
   docker-compose exec celery cat /app/processors/pdf.py | grep -E "(import|OpenAI|Mistral)"
   ```

2. **Verify the container is using the latest code**:
   ```bash
   docker-compose exec celery ls -la /app/processors/
   ```

3. **Check if old files exist in container**:
   ```bash
   docker-compose exec celery ls -la /app/processors/pdf*.py
   ```

## 🎉 **Success Indicators**

- ✅ No `401 Unauthorized` errors from OpenAI
- ✅ Successful `200 OK` responses from Mistral AI
- ✅ PDF processing completes without errors
- ✅ Semantic chunking works correctly
- ✅ Embeddings are generated successfully

---

**The migration is complete in the source code. You just need to restart your containers to pick up the changes!** 🚀
