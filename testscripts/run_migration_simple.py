#!/usr/bin/env python3
"""
Simple migration script to update database schema for Mistral AI embeddings.
"""

import os
import sys
from dotenv import load_dotenv
from db import DatabaseConnection

# Load environment variables
load_dotenv()

def run_migration():
    """Run the database migration for Mistral AI embeddings."""
    print("🔄 Starting migration to Mistral AI embeddings...")
    
    conn = DatabaseConnection()
    try:
        conn.connect()
        print("✅ Connected to database")
        
        # Step 1: Drop the existing vector index
        print("📝 Step 1: Dropping existing vector index...")
        try:
            conn.execute_query("DROP INDEX IF EXISTS embedding_vec_idx ON Document_Embeddings")
            print("✅ Vector index dropped")
        except Exception as e:
            print(f"⚠️  Index drop failed (might not exist): {e}")
        
        # Step 2: Drop the existing embedding column
        print("📝 Step 2: Dropping existing embedding column...")
        try:
            conn.execute_query("ALTER TABLE Document_Embeddings DROP COLUMN embedding")
            print("✅ Embedding column dropped")
        except Exception as e:
            print(f"⚠️  Column drop failed (might not exist): {e}")
        
        # Step 3: Add the new embedding column with correct dimensions for Mistral AI
        print("📝 Step 3: Adding new embedding column with 1024 dimensions...")
        try:
            conn.execute_query("ALTER TABLE Document_Embeddings ADD COLUMN embedding VECTOR(1024)")
            print("✅ New embedding column added")
        except Exception as e:
            print(f"❌ Failed to add embedding column: {e}")
            return False
        
        # Step 4: Recreate the vector index with Mistral AI dimensions
        print("📝 Step 4: Creating vector index...")
        try:
            conn.execute_query("""
                ALTER TABLE Document_Embeddings 
                ADD VECTOR INDEX embedding_vec_idx (embedding)
                INDEX_OPTIONS '{"index_type": "HNSW_FLAT", "metric_type": "DOT_PRODUCT", "M": 32, "efConstruction": 200}'
            """)
            print("✅ Vector index created")
        except Exception as e:
            print(f"❌ Failed to create vector index: {e}")
            return False
        
        print("\n🎉 Migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False
    finally:
        conn.disconnect()

def verify_migration():
    """Verify that the migration was successful."""
    print("\n🔍 Verifying migration...")
    
    conn = DatabaseConnection()
    try:
        conn.connect()
        
        # Check the table structure
        result = conn.execute_query("DESCRIBE Document_Embeddings")
        if result:
            print("\n📋 Current table structure:")
            for row in result:
                column_name, column_type = row[0], row[1]
                print(f"  {column_name}: {column_type}")
                if column_name == 'embedding' and 'vector(1024)' in column_type.lower():
                    print("✅ Migration verified: Embedding dimension is now 1024 (Mistral AI)")
                    return True
        
        print("❌ Migration verification failed")
        return False
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False
    finally:
        conn.disconnect()

def main():
    """Main function to run the migration and verification."""
    print("🚀 Mistral AI Embedding Migration Tool")
    print("=" * 50)
    
    # Run the migration
    if not run_migration():
        print("❌ Migration failed. Exiting.")
        sys.exit(1)
    
    # Verify the migration
    if not verify_migration():
        print("❌ Migration verification failed. Please check the database manually.")
        sys.exit(1)
    
    print("\n🎉 All done! Your database is now ready for Mistral AI embeddings.")
    print("💡 You can now restart your application and process documents with Mistral AI.")

if __name__ == "__main__":
    main()
