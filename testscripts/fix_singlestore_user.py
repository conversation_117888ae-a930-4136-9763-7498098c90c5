#!/usr/bin/env python3
"""
Script to fix SingleStore user permissions and database setup
"""
import os
import mysql.connector
from mysql.connector import <PERSON>rror
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def connect_as_root():
    """Connect to SingleStore as root to fix user permissions"""

    host = os.getenv("SINGLESTORE_HOST", "127.0.0.1")
    port = int(os.getenv("SINGLESTORE_PORT", "3306"))
    root_password = os.getenv("ROOT_PASSWORD", "2t]]zmczxuslsNuw31zg_e0M5")

    print("=== Connecting as Root to Fix User Permissions ===")
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"Root Password: {'*' * len(root_password) if root_password else 'NOT SET'}")
    print()

    try:
        # Connect as root
        connection = mysql.connector.connect(
            host=host,
            port=port,
            user='root',
            password=root_password,
            connect_timeout=10,
            ssl_disabled=True
        )

        if connection.is_connected():
            print("✅ Connected as root successfully!")
            cursor = connection.cursor()

            # Get current user info
            cursor.execute("SELECT USER(), @@hostname")
            user_info = cursor.fetchone()
            print(f"Connected as: {user_info[0]}")
            print(f"Server hostname: {user_info[1]}")

            return connection, cursor

    except Error as e:
        print(f"❌ Root connection failed!")
        print(f"Error Code: {e.errno}")
        print(f"Error Message: {e.msg}")
        return None, None

    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return None, None

def setup_database_and_user(cursor):
    """Set up database and user with proper permissions"""

    database = os.getenv("SINGLESTORE_DATABASE", "db_rag")
    user = os.getenv("SINGLESTORE_USER", "user_8dd7b")
    password = os.getenv("SINGLESTORE_PASSWORD", "2t]]zmczxuslsNuw31zg_e0M5")

    print(f"\n=== Setting up Database and User ===")
    print(f"Database: {database}")
    print(f"User: {user}")
    print(f"Password: {'*' * len(password) if password else 'NOT SET'}")

    try:
        # Create database if it doesn't exist
        print(f"\n🔄 Creating database '{database}'...")
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {database}")
        print(f"✅ Database '{database}' created/verified")

        # Drop user if exists (to recreate with proper permissions)
        print(f"\n🔄 Dropping existing user '{user}' if exists...")
        try:
            cursor.execute(f"DROP USER IF EXISTS '{user}'@'%'")
            cursor.execute(f"DROP USER IF EXISTS '{user}'@'localhost'")
            cursor.execute(f"DROP USER IF EXISTS '{user}'@'127.0.0.1'")
            print(f"✅ Existing user instances dropped")
        except Error as e:
            print(f"⚠️  User drop warning: {e.msg}")

        # Create user with wildcard host (allows any IP)
        print(f"\n🔄 Creating user '{user}' with wildcard host...")
        cursor.execute(f"CREATE USER '{user}'@'%' IDENTIFIED BY '{password}'")
        print(f"✅ User '{user}'@'%' created")

        # Grant all privileges on the database
        print(f"\n🔄 Granting privileges to '{user}' on database '{database}'...")
        cursor.execute(f"GRANT ALL PRIVILEGES ON {database}.* TO '{user}'@'%'")
        print(f"✅ Privileges granted")

        # Also create localhost version for local connections
        print(f"\n🔄 Creating localhost user for local connections...")
        cursor.execute(f"CREATE USER '{user}'@'localhost' IDENTIFIED BY '{password}'")
        cursor.execute(f"GRANT ALL PRIVILEGES ON {database}.* TO '{user}'@'localhost'")
        print(f"✅ Localhost user created and granted privileges")

        # Flush privileges
        print(f"\n🔄 Flushing privileges...")
        cursor.execute("FLUSH PRIVILEGES")
        print(f"✅ Privileges flushed")

        # Show created users (SingleStore uses information_schema)
        print(f"\n🔍 Verifying created users...")
        try:
            cursor.execute("SHOW GRANTS FOR %s@'%%'" % user)
            grants = cursor.fetchall()
            print(f"  Grants for {user}@'%':")
            for grant in grants:
                print(f"    {grant[0]}")
        except Error as e:
            print(f"  ⚠️  Could not show grants: {e.msg}")

        return True

    except Error as e:
        print(f"❌ Database/User setup failed!")
        print(f"Error Code: {e.errno}")
        print(f"Error Message: {e.msg}")
        return False

    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def test_user_connection():
    """Test connection with the newly created user"""

    host = os.getenv("SINGLESTORE_HOST", "127.0.0.1")
    port = int(os.getenv("SINGLESTORE_PORT", "3306"))
    user = os.getenv("SINGLESTORE_USER", "user_8dd7b")
    password = os.getenv("SINGLESTORE_PASSWORD", "2t]]zmczxuslsNuw31zg_e0M5")
    database = os.getenv("SINGLESTORE_DATABASE", "db_rag")

    print(f"\n=== Testing User Connection ===")

    try:
        connection = mysql.connector.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            connect_timeout=10,
            ssl_disabled=True
        )

        if connection.is_connected():
            print("✅ User connection successful!")

            cursor = connection.cursor()

            # Test basic operations
            cursor.execute("SELECT USER(), DATABASE(), VERSION()")
            result = cursor.fetchone()
            print(f"Connected as: {result[0]}")
            print(f"Database: {result[1]}")
            print(f"Version: {result[2]}")

            # Test table creation (to verify permissions)
            print(f"\n🔄 Testing table creation permissions...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS test_connection (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    test_message VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            cursor.execute("INSERT INTO test_connection (test_message) VALUES ('Connection test successful')")
            cursor.execute("SELECT * FROM test_connection ORDER BY created_at DESC LIMIT 1")
            test_result = cursor.fetchone()
            print(f"✅ Table operations successful: {test_result[1]}")

            cursor.close()
            connection.close()
            return True

    except Error as e:
        print(f"❌ User connection failed!")
        print(f"Error Code: {e.errno}")
        print(f"Error Message: {e.msg}")
        return False

    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def main():
    """Main function to fix SingleStore setup"""

    print("🔧 SingleStore User Permission Fix Script")
    print("=" * 50)

    # Step 1: Connect as root
    connection, cursor = connect_as_root()
    if not connection:
        print("\n❌ Cannot proceed without root connection")
        return False

    try:
        # Step 2: Setup database and user
        if not setup_database_and_user(cursor):
            print("\n❌ Database/User setup failed")
            return False

        # Close root connection
        cursor.close()
        connection.close()
        print("\n✅ Root connection closed")

        # Step 3: Test user connection
        if not test_user_connection():
            print("\n❌ User connection test failed")
            return False

        print("\n" + "=" * 50)
        print("🎉 SUCCESS! SingleStore setup is now complete!")
        print("You can now run your application with the configured user.")
        print("=" * 50)
        return True

    except Exception as e:
        print(f"\n❌ Unexpected error in main: {str(e)}")
        return False

    finally:
        if connection and connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
