#!/usr/bin/env python3
"""
Search Tracing Test Script

This script provides detailed tracing and analysis of the search functionality
to help understand how the search engine works. It includes:

1. Step-by-step search process tracing
2. Performance analysis of different search components
3. Chinese query testing (as requested: "列出所有庫易股票資料")
4. Search result quality analysis
5. Debug output generation

Usage:
    python test_search_traces.py
    python test_search_traces.py --query "列出所有庫易股票資料"
    python test_search_traces.py --api-url http://localhost:8000
    python test_search_traces.py --save-debug
"""

import os
import sys
import requests
import time
import json
import logging
import argparse
from typing import Dict, List, Any

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class SearchTracer:
    """Detailed search functionality tracer and analyzer."""
    
    def __init__(self, api_url: str = "http://localhost:8000", save_debug: bool = False):
        self.api_url = api_url.rstrip('/')
        self.api_key = os.getenv('API_KEY', 'a614192a822e6daef18a68029396879632c768dac57fe826043cf785bcdf519a7')
        self.save_debug = save_debug
        self.debug_dir = "search_debug_output"
        
        if self.save_debug:
            os.makedirs(self.debug_dir, exist_ok=True)
    
    def trace_search_query(self, query: str, top_k: int = 5) -> Dict[str, Any]:
        """Perform detailed tracing of a search query."""
        logger.info("=" * 80)
        logger.info(f"🔍 DETAILED SEARCH TRACE: '{query}'")
        logger.info("=" * 80)
        
        trace_data = {
            'query': query,
            'timestamp': time.time(),
            'top_k': top_k,
            'stages': {}
        }
        
        try:
            # Stage 1: API Request Setup
            stage_start = time.time()
            headers = {
                'X-API-Key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            search_data = {
                'query': query,
                'top_k': top_k,
                'debug': True
            }
            
            trace_data['stages']['request_setup'] = {
                'duration': time.time() - stage_start,
                'payload': search_data
            }
            
            logger.info(f"📋 STAGE 1: Request Setup")
            logger.info(f"   • Query: '{query}'")
            logger.info(f"   • Top K: {top_k}")
            logger.info(f"   • Debug mode: Enabled")
            logger.info(f"   • Setup time: {trace_data['stages']['request_setup']['duration']:.3f}s")
            
            # Stage 2: API Call
            stage_start = time.time()
            logger.info(f"\n🚀 STAGE 2: Making API Request")
            
            response = requests.post(
                f"{self.api_url}/kag-search", 
                headers=headers, 
                json=search_data, 
                timeout=60
            )
            
            api_duration = time.time() - stage_start
            trace_data['stages']['api_call'] = {
                'duration': api_duration,
                'status_code': response.status_code,
                'success': response.status_code == 200
            }
            
            logger.info(f"   • API call duration: {api_duration:.3f}s")
            logger.info(f"   • Response status: {response.status_code}")
            
            if response.status_code != 200:
                logger.error(f"❌ API request failed")
                logger.error(f"   Error: {response.text}")
                trace_data['error'] = response.text
                return trace_data
            
            # Stage 3: Response Parsing
            stage_start = time.time()
            search_results = response.json()
            
            trace_data['stages']['response_parsing'] = {
                'duration': time.time() - stage_start,
                'results_count': len(search_results.get('results', []))
            }
            
            logger.info(f"\n📊 STAGE 3: Response Analysis")
            logger.info(f"   • Parsing time: {trace_data['stages']['response_parsing']['duration']:.3f}s")
            logger.info(f"   • Results found: {trace_data['stages']['response_parsing']['results_count']}")
            
            # Stage 4: Detailed Result Analysis
            self._analyze_search_results(search_results, trace_data)
            
            # Stage 5: Save Debug Data
            if self.save_debug:
                self._save_debug_data(query, trace_data, search_results)
            
            return trace_data
            
        except Exception as e:
            logger.error(f"❌ Search tracing failed: {str(e)}")
            trace_data['error'] = str(e)
            return trace_data
    
    def _analyze_search_results(self, search_results: Dict, trace_data: Dict) -> None:
        """Perform detailed analysis of search results."""
        stage_start = time.time()
        
        results = search_results.get('results', [])
        execution_time = search_results.get('execution_time', 0)
        generated_response = search_results.get('generated_response', '')
        
        logger.info(f"\n🔬 STAGE 4: Detailed Results Analysis")
        logger.info(f"   📈 PERFORMANCE METRICS:")
        logger.info(f"      • Total execution time: {execution_time:.3f}s")
        logger.info(f"      • Results returned: {len(results)}")
        logger.info(f"      • AI response generated: {'Yes' if generated_response else 'No'}")
        
        if not results:
            logger.warning(f"   📭 No results found for analysis")
            trace_data['stages']['analysis'] = {
                'duration': time.time() - stage_start,
                'no_results': True
            }
            return
        
        # Analyze score distributions
        vector_scores = [r.get('vector_score', 0) for r in results]
        text_scores = [r.get('text_score', 0) for r in results]
        combined_scores = [r.get('combined_score', 0) for r in results]
        
        logger.info(f"   📊 SCORE ANALYSIS:")
        logger.info(f"      • Vector scores: min={min(vector_scores):.4f}, max={max(vector_scores):.4f}, avg={sum(vector_scores)/len(vector_scores):.4f}")
        logger.info(f"      • Text scores: min={min(text_scores):.4f}, max={max(text_scores):.4f}, avg={sum(text_scores)/len(text_scores):.4f}")
        logger.info(f"      • Combined scores: min={min(combined_scores):.4f}, max={max(combined_scores):.4f}, avg={sum(combined_scores)/len(combined_scores):.4f}")
        
        # Analyze content characteristics
        content_lengths = [len(r.get('content', '')) for r in results]
        entity_counts = [len(r.get('entities', [])) for r in results]
        relationship_counts = [len(r.get('relationships', [])) for r in results]
        
        logger.info(f"   📝 CONTENT ANALYSIS:")
        logger.info(f"      • Content lengths: min={min(content_lengths)}, max={max(content_lengths)}, avg={sum(content_lengths)/len(content_lengths):.0f}")
        logger.info(f"      • Entity counts: min={min(entity_counts)}, max={max(entity_counts)}, avg={sum(entity_counts)/len(entity_counts):.1f}")
        logger.info(f"      • Relationship counts: min={min(relationship_counts)}, max={max(relationship_counts)}, avg={sum(relationship_counts)/len(relationship_counts):.1f}")
        
        # Analyze top results in detail
        logger.info(f"   🏆 TOP RESULTS BREAKDOWN:")
        for i, result in enumerate(results[:3], 1):
            self._analyze_single_result(i, result)
        
        # Special analysis for Chinese queries
        query = trace_data.get('query', '')
        if any('\u4e00' <= char <= '\u9fff' for char in query):
            self._analyze_chinese_query_results(results)
        
        trace_data['stages']['analysis'] = {
            'duration': time.time() - stage_start,
            'score_stats': {
                'vector': {'min': min(vector_scores), 'max': max(vector_scores), 'avg': sum(vector_scores)/len(vector_scores)},
                'text': {'min': min(text_scores), 'max': max(text_scores), 'avg': sum(text_scores)/len(text_scores)},
                'combined': {'min': min(combined_scores), 'max': max(combined_scores), 'avg': sum(combined_scores)/len(combined_scores)}
            },
            'content_stats': {
                'lengths': {'min': min(content_lengths), 'max': max(content_lengths), 'avg': sum(content_lengths)/len(content_lengths)},
                'entities': {'min': min(entity_counts), 'max': max(entity_counts), 'avg': sum(entity_counts)/len(entity_counts)},
                'relationships': {'min': min(relationship_counts), 'max': max(relationship_counts), 'avg': sum(relationship_counts)/len(relationship_counts)}
            }
        }
    
    def _analyze_single_result(self, rank: int, result: Dict) -> None:
        """Analyze a single search result in detail."""
        doc_id = result.get('doc_id', 'N/A')
        content = result.get('content', '')
        vector_score = result.get('vector_score', 0)
        text_score = result.get('text_score', 0)
        combined_score = result.get('combined_score', 0)
        entities = result.get('entities', [])
        relationships = result.get('relationships', [])
        
        logger.info(f"      Result #{rank}:")
        logger.info(f"        • Doc ID: {doc_id}")
        logger.info(f"        • Scores: Vector={vector_score:.4f}, Text={text_score:.4f}, Combined={combined_score:.4f}")
        logger.info(f"        • Content: {len(content)} chars")
        logger.info(f"        • Knowledge: {len(entities)} entities, {len(relationships)} relationships")
        logger.info(f"        • Preview: {content[:100]}...")
        
        # Show top entities
        if entities:
            entity_names = [e.get('name', 'Unknown') for e in entities[:3]]
            logger.info(f"        • Top entities: {', '.join(entity_names)}")
    
    def _analyze_chinese_query_results(self, results: List[Dict]) -> None:
        """Special analysis for Chinese query results."""
        logger.info(f"   🇨🇳 CHINESE QUERY ANALYSIS:")
        
        chinese_content_count = 0
        stock_code_count = 0
        
        for result in results:
            content = result.get('content', '')
            
            # Check for Chinese content
            if any('\u4e00' <= char <= '\u9fff' for char in content):
                chinese_content_count += 1
            
            # Check for stock codes (4-digit numbers)
            import re
            if re.search(r'\b\d{4}\b', content):
                stock_code_count += 1
        
        logger.info(f"      • Results with Chinese content: {chinese_content_count}/{len(results)} ({(chinese_content_count/len(results)*100):.1f}%)")
        logger.info(f"      • Results with stock codes: {stock_code_count}/{len(results)} ({(stock_code_count/len(results)*100):.1f}%)")
    
    def _save_debug_data(self, query: str, trace_data: Dict, search_results: Dict) -> None:
        """Save debug data to files."""
        timestamp = int(time.time())
        
        # Save trace data
        trace_file = os.path.join(self.debug_dir, f"search_trace_{timestamp}.json")
        with open(trace_file, 'w', encoding='utf-8') as f:
            json.dump(trace_data, f, indent=2, ensure_ascii=False)
        
        # Save full search results
        results_file = os.path.join(self.debug_dir, f"search_results_{timestamp}.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(search_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"   💾 Debug data saved:")
        logger.info(f"      • Trace: {trace_file}")
        logger.info(f"      • Results: {results_file}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Detailed search functionality tracer")
    parser.add_argument("--api-url", default="http://localhost:8000", 
                       help="API backend URL")
    parser.add_argument("--query", type=str,
                       help="Custom search query to test")
    parser.add_argument("--top-k", type=int, default=5,
                       help="Number of results to retrieve")
    parser.add_argument("--save-debug", action="store_true",
                       help="Save debug output to files")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Verbose output")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create tracer
    tracer = SearchTracer(api_url=args.api_url, save_debug=args.save_debug)
    
    # Default test queries including the requested Chinese query
    default_queries = [
        "列出所有庫易股票資料",  # Chinese query as requested
        "What is Docker and how does it work?",
        "financial data analysis",
        "stock market information"
    ]
    
    # Use custom query if provided, otherwise use defaults
    queries = [args.query] if args.query else default_queries
    
    logger.info(f"🚀 Starting Search Tracing")
    logger.info(f"   API URL: {args.api_url}")
    logger.info(f"   Queries to test: {len(queries)}")
    logger.info(f"   Top K: {args.top_k}")
    logger.info(f"   Save debug: {args.save_debug}")
    
    success_count = 0
    
    for i, query in enumerate(queries, 1):
        logger.info(f"\n{'='*20} QUERY {i}/{len(queries)} {'='*20}")
        
        try:
            trace_data = tracer.trace_search_query(query, args.top_k)
            
            if 'error' not in trace_data:
                success_count += 1
                logger.info(f"✅ Query '{query}' traced successfully")
            else:
                logger.error(f"❌ Query '{query}' failed: {trace_data['error']}")
                
        except Exception as e:
            logger.error(f"❌ Unexpected error tracing query '{query}': {str(e)}")
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info(f"SEARCH TRACING SUMMARY")
    logger.info(f"{'='*60}")
    logger.info(f"Queries tested: {len(queries)}")
    logger.info(f"Successful traces: {success_count}")
    logger.info(f"Success rate: {(success_count/len(queries)*100):.1f}%")
    
    if success_count == len(queries):
        logger.info("🎉 All search traces completed successfully!")
    else:
        logger.warning("⚠️  Some search traces failed")
    
    return success_count == len(queries)

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Search tracing interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Search tracing failed: {str(e)}")
        sys.exit(1)
