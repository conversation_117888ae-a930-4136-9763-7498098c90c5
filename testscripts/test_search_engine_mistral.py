#!/usr/bin/env python3
"""
Test script to verify the search engine is working with Mistral AI.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from search.engine import RAGQueryEngine

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def test_search_engine():
    """Test the search engine with Mistral AI."""
    logger.info("🔍 Testing Search Engine with Mistral AI")
    logger.info("=" * 50)
    
    try:
        # Initialize the search engine
        logger.info("Initializing RAG Query Engine...")
        engine = RAGQueryEngine(debug_output=True)
        logger.info("✅ Search engine initialized successfully")
        
        # Test embedding generation
        logger.info("\n🧪 Testing embedding generation...")
        test_query = "What is Docker and how does it work?"
        embedding = engine.get_query_embedding(test_query)
        logger.info(f"✅ Generated embedding with {len(embedding)} dimensions")
        logger.info(f"   First 5 values: {embedding[:5]}")
        
        # Test search functionality (if database has data)
        logger.info(f"\n🔍 Testing search functionality...")
        logger.info(f"Query: '{test_query}'")
        
        try:
            response = engine.query(test_query, top_k=3)
            logger.info(f"✅ Search completed successfully")
            logger.info(f"   Found {len(response.results)} results")
            logger.info(f"   Generated response length: {len(response.generated_response)} characters")
            
            if response.results:
                logger.info("   Top result preview:")
                top_result = response.results[0]
                logger.info(f"     Doc ID: {top_result.doc_id}")
                logger.info(f"     Vector Score: {top_result.vector_score:.4f}")
                logger.info(f"     Text Score: {top_result.text_score:.4f}")
                logger.info(f"     Combined Score: {top_result.combined_score:.4f}")
                logger.info(f"     Content preview: {top_result.content[:100]}...")
            else:
                logger.info("   No results found (database may be empty)")
                
        except Exception as e:
            logger.warning(f"⚠️  Search test failed (likely no data in database): {str(e)}")
            logger.info("   This is normal if no documents have been processed yet")
        
        logger.info("\n🎉 Search engine test completed successfully!")
        logger.info("   Mistral AI integration is working correctly")
        return True
        
    except Exception as e:
        logger.error(f"❌ Search engine test failed: {str(e)}")
        return False

def test_client_selection():
    """Test the client selection logic."""
    logger.info("\n🔧 Testing client selection logic...")
    
    load_dotenv()
    
    mistral_key = os.getenv("MISTRAL_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY") 
    groq_key = os.getenv("GROQ_API_KEY")
    
    logger.info(f"   MISTRAL_API_KEY present: {bool(mistral_key)}")
    logger.info(f"   OPENAI_API_KEY present: {bool(openai_key)}")
    logger.info(f"   GROQ_API_KEY present: {bool(groq_key)}")
    
    try:
        engine = RAGQueryEngine()
        
        # Check what client is being used for embeddings
        logger.info(f"   Embedding client type: {type(engine.embedding_client).__name__}")
        logger.info(f"   Embedding model: {engine.embedding_model}")
        
        # Check what client is being used for response generation
        logger.info(f"   Response client type: {type(engine.response_client).__name__}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Client selection test failed: {str(e)}")
        return False

def main():
    """Main function."""
    logger.info("🚀 Starting Mistral AI Search Engine Tests")
    
    success_count = 0
    total_tests = 2
    
    # Test 1: Basic search engine functionality
    if test_search_engine():
        success_count += 1
    
    # Test 2: Client selection logic
    if test_client_selection():
        success_count += 1
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("TEST SUMMARY")
    logger.info("=" * 50)
    logger.info(f"Tests passed: {success_count}/{total_tests}")
    logger.info(f"Success rate: {(success_count/total_tests)*100:.1f}%")
    
    if success_count == total_tests:
        logger.info("🎉 ALL TESTS PASSED!")
        logger.info("   Your search engine is working perfectly with Mistral AI")
    else:
        logger.warning("⚠️  Some tests failed")
        logger.info("   Check the error messages above for details")
    
    return success_count == total_tests

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")
        sys.exit(1)
