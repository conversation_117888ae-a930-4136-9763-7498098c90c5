# API Testing with curl Commands

This document provides ready-to-use curl commands for testing the PDF processing API endpoints.

## Configuration

```bash
# Set these variables for your environment
API_BASE_URL="http://localhost:8000"
API_KEY="a614192a822e6daef18a68029396879632c768dac57fe826043cf785bcdf519a7"
PDF_FILE="documents/sample_test_document.pdf"
```

## 1. Check Server Status

```bash
# Check if server is running
curl -s "${API_BASE_URL}/docs" | head -n 5

# Check API documentation
curl -s "${API_BASE_URL}/openapi.json" | jq '.info'
```

## 2. Upload PDF Document

```bash
# Upload a PDF file
curl -X POST \
  -H "X-API-Key: ${API_KEY}" \
  -F "file=@${PDF_FILE}" \
  "${API_BASE_URL}/upload-pdf" | jq '.'

# Example response:
# {
#   "task_id": "abc123-def456-ghi789",
#   "doc_id": 42,
#   "status": "pending",
#   "message": "Processing started"
# }
```

## 3. Monitor Task Status

```bash
# Check Celery task status (replace TASK_ID with actual task ID)
TASK_ID="abc123-def456-ghi789"
curl -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/task-status/${TASK_ID}" | jq '.'

# Example response:
# {
#   "task_id": "abc123-def456-ghi789",
#   "status": "SUCCESS",
#   "message": "Processing completed",
#   "current": 100,
#   "total": 100
# }
```

## 4. Check Processing Status

```bash
# Check document processing status (replace DOC_ID with actual document ID)
DOC_ID=42
curl -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/processing-status/${DOC_ID}" | jq '.'

# Example response:
# {
#   "currentStep": "completed",
#   "fileName": "sample_test_document.pdf"
# }
```

## 5. Retrieve Document Chunks

```bash
# Get processed document chunks
curl -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/doc-chunks?doc_id=${DOC_ID}" | jq '.'

# Example response:
# {
#   "chunks": [
#     {
#       "content": "This is the first chunk of the document..."
#     },
#     {
#       "content": "This is the second chunk of the document..."
#     }
#   ]
# }

# Count chunks
curl -s -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/doc-chunks?doc_id=${DOC_ID}" | jq '.chunks | length'

# Get first chunk preview
curl -s -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/doc-chunks?doc_id=${DOC_ID}" | jq -r '.chunks[0].content' | head -c 200
```

## 6. Search Documents

```bash
# Perform semantic search
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${API_KEY}" \
  -d '{
    "query": "document processing system",
    "top_k": 5,
    "debug": false
  }' \
  "${API_BASE_URL}/kag-search" | jq '.'

# Search with debug information
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${API_KEY}" \
  -d '{
    "query": "API endpoints",
    "top_k": 3,
    "debug": true
  }' \
  "${API_BASE_URL}/kag-search" | jq '.'

# Get only the generated response
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${API_KEY}" \
  -d '{"query": "technical architecture", "top_k": 3}' \
  "${API_BASE_URL}/kag-search" | jq -r '.generated_response'
```

## 7. Get Knowledge Base Statistics

```bash
# Get KB statistics
curl -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/kbdata" | jq '.'

# Get document count only
curl -s -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/kbdata" | jq '.stats.total_documents'

# Get recent documents
curl -s -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/kbdata" | jq '.stats.documents[]'
```

## 8. Get Graph Data

```bash
# Get knowledge graph data
curl -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/graph-data" | jq '.'

# Count nodes and links
curl -s -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/graph-data" | jq '{nodes: (.data.nodes | length), links: (.data.links | length)}'
```

## 9. Configuration Management

```bash
# Get current configuration
curl -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/config" | jq '.'

# Update configuration (example)
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${API_KEY}" \
  -d '{
    "knowledge_creation": {
      "chunking": {
        "semantic_rules": ["paragraph", "section"],
        "overlap_size": 50,
        "min_chunk_size": 100,
        "max_chunk_size": 1000
      },
      "entity_extraction": {
        "model": "gpt-4",
        "confidence_threshold": 0.8,
        "min_description_length": 10,
        "max_description_length": 500,
        "description_required": true,
        "system_prompt": "Extract entities...",
        "extraction_prompt_template": "Find entities in: {text}"
      }
    },
    "retrieval": {
      "search": {
        "top_k": 10,
        "vector_weight": 0.7,
        "text_weight": 0.3,
        "exact_phrase_weight": 1.5,
        "single_term_weight": 0.8,
        "proximity_distance": 5,
        "min_score_threshold": 0.1,
        "min_similarity_score": 0.3,
        "context_window_size": 200
      },
      "response_generation": {
        "temperature": 0.7,
        "max_tokens": 500,
        "citation_style": "numbered",
        "include_confidence": true,
        "prompt_template": "Answer based on: {context}"
      }
    }
  }' \
  "${API_BASE_URL}/config"
```

## 10. Error Handling Examples

```bash
# Test with invalid API key
curl -X POST \
  -H "X-API-Key: invalid_key" \
  -F "file=@${PDF_FILE}" \
  "${API_BASE_URL}/upload-pdf"

# Test with non-existent document ID
curl -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/processing-status/99999"

# Test with invalid task ID
curl -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/task-status/invalid-task-id"
```

## 11. Batch Testing Script

```bash
#!/bin/bash
# Complete workflow test

API_BASE_URL="http://localhost:8000"
API_KEY="a614192a822e6daef18a68029396879632c768dac57fe826043cf785bcdf519a7"
PDF_FILE="documents/sample_test_document.pdf"

echo "1. Uploading PDF..."
UPLOAD_RESPONSE=$(curl -s -X POST \
  -H "X-API-Key: ${API_KEY}" \
  -F "file=@${PDF_FILE}" \
  "${API_BASE_URL}/upload-pdf")

echo "$UPLOAD_RESPONSE" | jq '.'

TASK_ID=$(echo "$UPLOAD_RESPONSE" | jq -r '.task_id')
DOC_ID=$(echo "$UPLOAD_RESPONSE" | jq -r '.doc_id')

echo "2. Monitoring task: $TASK_ID"
while true; do
  STATUS_RESPONSE=$(curl -s -H "X-API-Key: ${API_KEY}" \
    "${API_BASE_URL}/task-status/${TASK_ID}")
  
  STATUS=$(echo "$STATUS_RESPONSE" | jq -r '.status')
  echo "Status: $STATUS"
  
  if [ "$STATUS" = "SUCCESS" ] || [ "$STATUS" = "FAILURE" ]; then
    break
  fi
  
  sleep 5
done

echo "3. Getting document chunks..."
curl -s -H "X-API-Key: ${API_KEY}" \
  "${API_BASE_URL}/doc-chunks?doc_id=${DOC_ID}" | jq '.chunks | length'

echo "4. Testing search..."
curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: ${API_KEY}" \
  -d '{"query": "document processing", "top_k": 3}' \
  "${API_BASE_URL}/kag-search" | jq '.results | length'

echo "Workflow complete!"
```

## Tips

1. **Install jq for JSON formatting**: `brew install jq` (macOS) or `apt-get install jq` (Ubuntu)

2. **Save responses to files**:
   ```bash
   curl -H "X-API-Key: ${API_KEY}" "${API_BASE_URL}/kbdata" > kb_stats.json
   ```

3. **Use variables for repeated values**:
   ```bash
   export API_KEY="your_api_key_here"
   export API_BASE_URL="http://localhost:8000"
   ```

4. **Check HTTP status codes**:
   ```bash
   curl -w "%{http_code}" -s -o /dev/null -H "X-API-Key: ${API_KEY}" "${API_BASE_URL}/docs"
   ```

5. **Time requests**:
   ```bash
   time curl -H "X-API-Key: ${API_KEY}" "${API_BASE_URL}/kag-search" -d '{"query": "test"}'
   ```
