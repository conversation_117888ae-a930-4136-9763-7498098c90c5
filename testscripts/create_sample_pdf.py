#!/usr/bin/env python3
"""
Create a sample PDF for testing the document processing API.

This script creates a realistic PDF document with multiple sections
that can be used to test the PDF processing pipeline.
"""

import os
import sys
from pathlib import Path

def create_pdf_with_reportlab(output_path: str) -> bool:
    """Create PDF using reportlab library."""
    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        
        # Create document
        doc = SimpleDocTemplate(output_path, pagesize=letter,
                              rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)
        
        # Get styles
        styles = getSampleStyleSheet()
        
        # Create custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            spaceBefore=20
        )
        
        # Build content
        story = []
        
        # Title
        story.append(Paragraph("Sample Document for API Testing", title_style))
        story.append(Spacer(1, 20))
        
        # Introduction
        story.append(Paragraph("Introduction", heading_style))
        story.append(Paragraph("""
        This is a comprehensive test document created specifically for validating
        the PDF processing API endpoints. It contains multiple sections with
        varied content to test the chunking and embedding generation capabilities
        of the document processing system.
        """, styles['Normal']))
        story.append(Spacer(1, 12))
        
        # Section 1
        story.append(Paragraph("System Overview", heading_style))
        story.append(Paragraph("""
        The document processing system is designed to handle PDF files and extract
        meaningful information through advanced natural language processing techniques.
        Key components include:
        """, styles['Normal']))
        
        story.append(Paragraph("• PDF text extraction and parsing", styles['Normal']))
        story.append(Paragraph("• Intelligent semantic chunking", styles['Normal']))
        story.append(Paragraph("• High-dimensional embedding generation", styles['Normal']))
        story.append(Paragraph("• Knowledge graph construction and entity extraction", styles['Normal']))
        story.append(Paragraph("• Real-time processing status monitoring", styles['Normal']))
        story.append(Spacer(1, 12))
        
        # Section 2
        story.append(Paragraph("Technical Architecture", heading_style))
        story.append(Paragraph("""
        The system employs a microservices architecture with the following components:
        """, styles['Normal']))
        story.append(Spacer(1, 6))
        
        story.append(Paragraph("API Layer", styles['Heading3']))
        story.append(Paragraph("""
        FastAPI-based REST endpoints provide authentication, authorization,
        file upload handling, and status monitoring capabilities.
        """, styles['Normal']))
        story.append(Spacer(1, 6))
        
        story.append(Paragraph("Processing Pipeline", styles['Heading3']))
        story.append(Paragraph("""
        Asynchronous task processing with Celery handles PDF parsing with multiple
        fallback strategies, semantic chunking using AI models, and embedding
        generation with OpenAI models.
        """, styles['Normal']))
        story.append(Spacer(1, 6))
        
        story.append(Paragraph("Data Storage", styles['Heading3']))
        story.append(Paragraph("""
        SingleStore database provides vector storage for document metadata,
        processing status, and knowledge graph entities and relationships.
        """, styles['Normal']))
        story.append(Spacer(1, 12))
        
        # Section 3
        story.append(Paragraph("API Endpoints", heading_style))
        story.append(Paragraph("""
        The system provides several key endpoints for document processing:
        """, styles['Normal']))
        
        endpoints = [
            ("/upload-pdf", "Upload and initiate processing"),
            ("/processing-status/{doc_id}", "Monitor processing progress"),
            ("/task-status/{task_id}", "Check Celery task status"),
            ("/doc-chunks", "Retrieve processed document chunks"),
            ("/kag-search", "Perform semantic search queries")
        ]
        
        for endpoint, description in endpoints:
            story.append(Paragraph(f"<b>{endpoint}</b> - {description}", styles['Normal']))
        
        story.append(Spacer(1, 12))
        
        # Section 4
        story.append(Paragraph("Testing Scenarios", heading_style))
        story.append(Paragraph("""
        This document tests various processing scenarios including:
        """, styles['Normal']))
        
        story.append(Paragraph("• Multi-section document structure", styles['Normal']))
        story.append(Paragraph("• Technical terminology and concepts", styles['Normal']))
        story.append(Paragraph("• Lists and structured content", styles['Normal']))
        story.append(Paragraph("• Code-like formatting and technical details", styles['Normal']))
        story.append(Spacer(1, 12))
        
        # Conclusion
        story.append(Paragraph("Conclusion", heading_style))
        story.append(Paragraph("""
        This test document provides comprehensive coverage for validating
        the document processing pipeline from upload through search functionality.
        The varied content ensures robust testing of all system components
        and validates the effectiveness of the semantic chunking and
        embedding generation processes.
        """, styles['Normal']))
        
        # Build PDF
        doc.build(story)
        return True
        
    except ImportError:
        print("reportlab not installed. Install with: pip install reportlab")
        return False
    except Exception as e:
        print(f"Error creating PDF with reportlab: {e}")
        return False


def create_pdf_with_fpdf(output_path: str) -> bool:
    """Create PDF using fpdf library."""
    try:
        from fpdf import FPDF
        
        class PDF(FPDF):
            def header(self):
                self.set_font('Arial', 'B', 15)
                self.cell(0, 10, 'Sample Document for API Testing', 0, 1, 'C')
                self.ln(10)
            
            def footer(self):
                self.set_y(-15)
                self.set_font('Arial', 'I', 8)
                self.cell(0, 10, f'Page {self.page_no()}', 0, 0, 'C')
        
        pdf = PDF()
        pdf.add_page()
        pdf.set_font('Arial', '', 12)
        
        # Content sections
        sections = [
            ("Introduction", """
This is a comprehensive test document created specifically for validating
the PDF processing API endpoints. It contains multiple sections with
varied content to test the chunking and embedding generation capabilities
of the document processing system.
"""),
            ("System Overview", """
The document processing system is designed to handle PDF files and extract
meaningful information through advanced natural language processing techniques.
Key components include PDF text extraction, intelligent semantic chunking,
high-dimensional embedding generation, knowledge graph construction,
and real-time processing status monitoring.
"""),
            ("Technical Architecture", """
The system employs a microservices architecture with API layer for REST endpoints,
processing pipeline for asynchronous task handling, and data storage using
SingleStore database for vector storage and metadata management.
"""),
            ("API Endpoints", """
The system provides key endpoints including /upload-pdf for file upload,
/processing-status for monitoring progress, /task-status for Celery tasks,
/doc-chunks for retrieving processed content, and /kag-search for semantic queries.
"""),
            ("Testing Scenarios", """
This document tests various processing scenarios including multi-section structure,
technical terminology, lists and structured content, and code-like formatting
to ensure comprehensive validation of all system components.
"""),
            ("Conclusion", """
This test document provides comprehensive coverage for validating the document
processing pipeline from upload through search functionality. The varied content
ensures robust testing of semantic chunking and embedding generation processes.
""")
        ]
        
        for title, content in sections:
            # Add section title
            pdf.set_font('Arial', 'B', 14)
            pdf.cell(0, 10, title, 0, 1)
            pdf.ln(5)
            
            # Add section content
            pdf.set_font('Arial', '', 12)
            # Split content into lines to fit page width
            lines = content.strip().split('\n')
            for line in lines:
                if line.strip():
                    pdf.cell(0, 6, line.strip(), 0, 1)
            pdf.ln(10)
        
        pdf.output(output_path)
        return True
        
    except ImportError:
        print("fpdf not installed. Install with: pip install fpdf2")
        return False
    except Exception as e:
        print(f"Error creating PDF with fpdf: {e}")
        return False


def create_text_file(output_path: str) -> bool:
    """Create a text file as fallback."""
    try:
        content = """# Sample Document for API Testing

## Introduction

This is a comprehensive test document created specifically for validating
the PDF processing API endpoints. It contains multiple sections with
varied content to test the chunking and embedding generation capabilities
of the document processing system.

## System Overview

The document processing system is designed to handle PDF files and extract
meaningful information through advanced natural language processing techniques.
Key components include:

- PDF text extraction and parsing
- Intelligent semantic chunking
- High-dimensional embedding generation
- Knowledge graph construction and entity extraction
- Real-time processing status monitoring

## Technical Architecture

The system employs a microservices architecture with the following components:

### API Layer
FastAPI-based REST endpoints provide authentication, authorization,
file upload handling, and status monitoring capabilities.

### Processing Pipeline
Asynchronous task processing with Celery handles PDF parsing with multiple
fallback strategies, semantic chunking using AI models, and embedding
generation with OpenAI models.

### Data Storage
SingleStore database provides vector storage for document metadata,
processing status, and knowledge graph entities and relationships.

## API Endpoints

The system provides several key endpoints for document processing:

1. /upload-pdf - Upload and initiate processing
2. /processing-status/{doc_id} - Monitor processing progress
3. /task-status/{task_id} - Check Celery task status
4. /doc-chunks - Retrieve processed document chunks
5. /kag-search - Perform semantic search queries

## Testing Scenarios

This document tests various processing scenarios including:

- Multi-section document structure
- Technical terminology and concepts
- Lists and structured content
- Code-like formatting and technical details

## Conclusion

This test document provides comprehensive coverage for validating
the document processing pipeline from upload through search functionality.
The varied content ensures robust testing of all system components
and validates the effectiveness of the semantic chunking and
embedding generation processes.
"""
        
        txt_path = output_path.replace('.pdf', '.txt')
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Created text file: {txt_path}")
        print("Please convert to PDF manually or install a PDF library:")
        print("  pip install reportlab")
        print("  pip install fpdf2")
        return False
        
    except Exception as e:
        print(f"Error creating text file: {e}")
        return False


def main():
    """Main function to create sample PDF."""
    output_path = "documents/sample_test_document.pdf"
    
    # Create documents directory
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Check if PDF already exists
    if os.path.exists(output_path):
        print(f"PDF already exists: {output_path}")
        return True
    
    print("Creating sample PDF for testing...")
    
    # Try different PDF creation methods
    if create_pdf_with_reportlab(output_path):
        print(f"✓ PDF created successfully with reportlab: {output_path}")
        return True
    
    if create_pdf_with_fpdf(output_path):
        print(f"✓ PDF created successfully with fpdf: {output_path}")
        return True
    
    # Fallback to text file
    print("No PDF libraries available, creating text file instead...")
    create_text_file(output_path)
    return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
