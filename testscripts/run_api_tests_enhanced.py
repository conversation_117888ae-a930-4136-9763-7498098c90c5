#!/usr/bin/env python3
"""
Enhanced API Test Runner with Custom PDF Support

This script extends the original run_api_tests.py to support custom PDF files.
It can copy any PDF file to the documents directory and process it.

Usage:
    python run_api_tests_enhanced.py                           # Use default sample PDF
    python run_api_tests_enhanced.py --pdf /path/to/file.pdf   # Use custom PDF
    python run_api_tests_enhanced.py --pdf file.pdf --copy     # Copy to documents first
"""

import os
import sys
import shutil
import argparse
import logging
from pathlib import Path
from typing import Optional

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import the original test runner
from testscripts.run_api_tests import APITestRunner

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class EnhancedAPITestRunner(APITestRunner):
    """Enhanced API test runner with custom PDF support."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        super().__init__(base_url)
        self.documents_dir = Path("documents")
        self.documents_dir.mkdir(exist_ok=True)
        self.custom_pdf_path = None
    
    def set_custom_pdf(self, pdf_path: str, copy_to_docs: bool = False, 
                      target_name: Optional[str] = None) -> bool:
        """Set a custom PDF file for testing."""
        source_path = Path(pdf_path)
        
        # Validate source file
        if not source_path.exists():
            logger.error(f"PDF file not found: {pdf_path}")
            return False
        
        if not source_path.suffix.lower() == '.pdf':
            logger.error(f"File is not a PDF: {pdf_path}")
            return False
        
        # Validate PDF content
        try:
            with open(source_path, 'rb') as f:
                header = f.read(8)
                if not header.startswith(b'%PDF-'):
                    logger.error(f"File does not appear to be a valid PDF: {pdf_path}")
                    return False
        except Exception as e:
            logger.error(f"Error reading PDF file: {e}")
            return False
        
        if copy_to_docs:
            # Copy to documents directory
            if target_name:
                if not target_name.endswith('.pdf'):
                    target_name += '.pdf'
                target_path = self.documents_dir / target_name
            else:
                target_path = self.documents_dir / source_path.name
            
            try:
                shutil.copy2(source_path, target_path)
                logger.info(f"✓ PDF copied to: {target_path}")
                self.custom_pdf_path = str(target_path)
            except Exception as e:
                logger.error(f"Failed to copy PDF: {e}")
                return False
        else:
            self.custom_pdf_path = str(source_path)
        
        file_size = Path(self.custom_pdf_path).stat().st_size
        logger.info(f"✓ Custom PDF set: {self.custom_pdf_path}")
        logger.info(f"  Size: {file_size:,} bytes")
        
        return True
    
    def create_sample_pdf(self) -> bool:
        """Override to use custom PDF if set, otherwise create sample."""
        if self.custom_pdf_path:
            logger.info(f"Using custom PDF: {self.custom_pdf_path}")
            return os.path.exists(self.custom_pdf_path)
        else:
            # Use original method
            return super().create_sample_pdf()
    
    def run_python_tests(self) -> bool:
        """Override to use custom PDF path."""
        logger.info("Running Python-based API tests...")
        
        # Look for test_api_python.py in the script directory
        script_dir = Path(__file__).parent.absolute()
        python_test_path = script_dir / 'test_api_python.py'
        if not python_test_path.exists():
            logger.error(f"test_api_python.py not found in {script_dir}")
            return False
        
        try:
            # Build command arguments
            cmd_args = [
                sys.executable, str(python_test_path), 
                '--url', self.base_url
            ]
            
            # If we have a custom PDF, we need to modify the test to use it
            if self.custom_pdf_path:
                # We'll use the custom PDF tester instead
                custom_test_path = script_dir / 'test_with_custom_pdf.py'
                if custom_test_path.exists():
                    cmd_args = [
                        sys.executable, str(custom_test_path),
                        '--pdf', self.custom_pdf_path,
                        '--url', self.base_url
                    ]
                    logger.info(f"Using custom PDF tester with: {self.custom_pdf_path}")
            
            import subprocess
            result = subprocess.run(cmd_args, capture_output=True, text=True, timeout=120)
            
            logger.info("Python test output:")
            if result.stdout:
                print(result.stdout)
            if result.stderr:
                print(result.stderr, file=sys.stderr)
            
            success = result.returncode == 0
            self.test_results['python_tests'] = success
            
            if success:
                logger.info("✓ Python tests completed successfully")
            else:
                logger.error("✗ Python tests failed")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to run Python tests: {e}")
            self.test_results['python_tests'] = False
            return False
    
    def list_documents(self) -> None:
        """List all files in the documents directory."""
        logger.info("Files in documents directory:")
        
        if not self.documents_dir.exists():
            logger.info("  (documents directory does not exist)")
            return
        
        files = list(self.documents_dir.glob("*"))
        if not files:
            logger.info("  (no files found)")
            return
        
        for file_path in sorted(files):
            if file_path.is_file():
                size = file_path.stat().st_size
                logger.info(f"  {file_path.name} ({size:,} bytes)")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Run comprehensive API tests with optional custom PDF",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run tests with default sample PDF
  python run_api_tests_enhanced.py
  
  # Run tests with custom PDF (direct path)
  python run_api_tests_enhanced.py --pdf /path/to/document.pdf
  
  # Copy custom PDF to documents directory first
  python run_api_tests_enhanced.py --pdf /path/to/document.pdf --copy
  
  # Copy with custom name
  python run_api_tests_enhanced.py --pdf document.pdf --copy --name test_doc.pdf
  
  # List files in documents directory
  python run_api_tests_enhanced.py --list-docs
        """
    )
    
    # Original arguments
    parser.add_argument("--url", default="http://localhost:8000", help="API base URL")
    parser.add_argument("--skip-server-check", action="store_true", help="Skip server availability check")
    parser.add_argument("--quick-only", action="store_true", help="Run only quick tests")
    parser.add_argument("--python-only", action="store_true", help="Run only Python tests")
    parser.add_argument("--curl-only", action="store_true", help="Run only curl tests")
    parser.add_argument("--skip-python", action="store_true", help="Skip Python tests")
    parser.add_argument("--skip-curl", action="store_true", help="Skip curl tests")
    parser.add_argument("--skip-pdf-creation", action="store_true", help="Skip sample PDF creation")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    # New arguments for custom PDF support
    parser.add_argument("--pdf", help="Path to custom PDF file to test")
    parser.add_argument("--copy", action="store_true", help="Copy PDF to documents directory")
    parser.add_argument("--name", help="Custom name for copied PDF file")
    parser.add_argument("--list-docs", action="store_true", help="List documents directory and exit")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create enhanced test runner
    runner = EnhancedAPITestRunner(base_url=args.url)
    
    # Handle list-docs command
    if args.list_docs:
        runner.list_documents()
        return
    
    # Validate arguments
    if args.name and not args.copy:
        logger.error("--name can only be used with --copy")
        sys.exit(1)
    
    # Set custom PDF if provided
    if args.pdf:
        if not runner.set_custom_pdf(args.pdf, args.copy, args.name):
            logger.error("Failed to set custom PDF")
            sys.exit(1)
    
    # Run tests based on arguments (same logic as original)
    try:
        if args.quick_only:
            success = runner.run_quick_test()
        elif args.python_only:
            runner.check_dependencies()
            if not args.skip_pdf_creation:
                runner.create_sample_pdf()
            if not args.skip_server_check:
                runner.check_server()
            success = runner.run_python_tests()
        elif args.curl_only:
            runner.check_dependencies()
            if not args.skip_pdf_creation:
                runner.create_sample_pdf()
            if not args.skip_server_check:
                runner.check_server()
            success = runner.run_curl_tests()
        else:
            # Run all tests
            success = runner.run_all_tests(
                skip_server_check=args.skip_server_check,
                skip_python=args.skip_python,
                skip_curl=args.skip_curl,
                skip_pdf_creation=args.skip_pdf_creation
            )
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
