#!/usr/bin/env python3
"""
Test script to verify the worker is using Mistral AI instead of Google API.
"""

import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from processors.pdf import process_pdf

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def test_pdf_processing():
    """Test PDF processing to ensure it uses Mistral AI."""
    logger.info("🧪 Testing PDF Processing with Mistral AI")
    logger.info("=" * 50)

    # Check if sample PDF exists
    sample_pdf = Path("documents/sample_test_document.pdf")
    if not sample_pdf.exists():
        logger.warning("Sample PDF not found, creating one...")
        # Try to create a sample PDF
        try:
            from testscripts.create_sample_pdf import create_sample_pdf
            create_sample_pdf()
            if not sample_pdf.exists():
                logger.error("❌ Could not create sample PDF")
                return False
        except Exception as e:
            logger.error(f"❌ Failed to create sample PDF: {e}")
            return False

    logger.info(f"Using PDF: {sample_pdf}")

    try:
        # Process the PDF document
        logger.info("Processing PDF document...")
        # Process the PDF document (this function expects a doc_id, not file path)
        # We need to create a document record first
        from processors.pdf import create_document_record

        # Get file info
        file_size = sample_pdf.stat().st_size
        filename = sample_pdf.name
        file_path = str(sample_pdf)

        # Create document record
        doc_id = create_document_record(filename, file_path, file_size)
        logger.info(f"Created document record with ID: {doc_id}")

        # Process the PDF
        process_pdf(doc_id)

        # The process_pdf function doesn't return a result, it processes and stores in DB
        # If we get here without exception, it was successful
        logger.info("✅ PDF processing completed successfully!")
        logger.info(f"   Document ID: {doc_id}")

        # Check processing status
        from processors.pdf import get_processing_status
        status = get_processing_status(doc_id)
        if status:
            logger.info(f"   Status: {status.get('status', 'unknown')}")
            logger.info(f"   Progress: {status.get('progress', 0)}%")

        return True

    except Exception as e:
        error_msg = str(e)
        logger.error(f"❌ PDF processing failed with exception: {error_msg}")

        # Check if it's still trying to use Google API
        if "googleapis.com" in error_msg or "API_KEY_INVALID" in error_msg:
            logger.error("🚨 STILL USING GOOGLE API! The migration is incomplete.")
            logger.error("   This should now be using Mistral AI instead.")
        elif "MISTRAL_API_KEY" in error_msg:
            logger.error("🔑 Mistral API key issue. Please check your .env file.")
        else:
            logger.error("🔍 Unknown error. Check the full traceback above.")

        return False

def check_environment():
    """Check environment variables."""
    logger.info("🔍 Checking Environment Variables")
    logger.info("=" * 50)

    load_dotenv()

    mistral_key = os.getenv('MISTRAL_API_KEY')
    google_key = os.getenv('GEMINI_API_KEY')

    logger.info(f"MISTRAL_API_KEY present: {bool(mistral_key)}")
    logger.info(f"GEMINI_API_KEY present: {bool(google_key)}")

    if mistral_key:
        logger.info(f"✅ Mistral API key found: {mistral_key[:8]}...{mistral_key[-4:]}")
    else:
        logger.error("❌ MISTRAL_API_KEY not found!")
        return False

    if google_key:
        logger.warning("⚠️  GEMINI_API_KEY still present (should not be needed)")

    return True

def test_imports():
    """Test that imports are working correctly."""
    logger.info("📦 Testing Imports")
    logger.info("=" * 50)

    try:
        # Test Mistral import
        from mistralai import Mistral
        logger.info("✅ Mistral AI import successful")

        # Test that we're not importing Google AI
        try:
            import google.generativeai as genai
            logger.warning("⚠️  Google Generative AI is still importable (but should not be used)")
        except ImportError:
            logger.info("✅ Google Generative AI not imported (good)")

        # Test PDF processor imports
        from processors.pdf import get_semantic_chunks, sliding_window_chunking
        logger.info("✅ PDF processor imports successful")

        return True

    except ImportError as e:
        logger.error(f"❌ Import failed: {e}")
        return False

def main():
    """Main function to run all tests."""
    logger.info("🚀 Testing Worker with Mistral AI Migration")

    tests = [
        ("Environment Check", check_environment),
        ("Import Test", test_imports),
        ("PDF Processing", test_pdf_processing),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*60}")

        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {str(e)}")

    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*60}")
    logger.info(f"Tests passed: {passed}/{total}")
    logger.info(f"Success rate: {(passed/total)*100:.1f}%")

    if passed == total:
        logger.info("🎉 ALL TESTS PASSED!")
        logger.info("   The worker should now use Mistral AI instead of Google API.")
        logger.info("   No more 'googleapis.com' errors should occur.")
    else:
        logger.warning("⚠️  Some tests failed.")
        logger.info("   Please review the issues above before running the worker.")

    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")
        sys.exit(1)
