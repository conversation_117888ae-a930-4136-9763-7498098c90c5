#!/usr/bin/env python3
"""
Debug script to check what's actually running in the Docker container.
"""

import subprocess
import sys
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def run_command(cmd, description):
    """Run a command and return the output."""
    logger.info(f"🔍 {description}")
    logger.info(f"Command: {cmd}")
    logger.info("-" * 50)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        if result.stdout:
            logger.info("STDOUT:")
            logger.info(result.stdout)
        if result.stderr:
            logger.info("STDERR:")
            logger.info(result.stderr)
        logger.info(f"Return code: {result.returncode}")
        return result.stdout, result.stderr, result.returncode
    except subprocess.TimeoutExpired:
        logger.error("Command timed out!")
        return "", "Timeout", 1
    except Exception as e:
        logger.error(f"Error running command: {e}")
        return "", str(e), 1

def main():
    """Main debugging function."""
    logger.info("🐛 Debugging Docker Container Issues")
    logger.info("=" * 60)
    
    commands = [
        # Check if containers are running
        ("docker-compose ps", "Check container status"),
        
        # Check what PDF files exist in the container
        ("docker-compose exec celery ls -la /app/processors/", "List processor files in container"),
        
        # Check the actual content of the PDF processor in the container
        ("docker-compose exec celery head -20 /app/processors/pdf.py", "Check PDF processor imports in container"),
        
        # Check for OpenAI imports in the container
        ("docker-compose exec celery grep -n 'from openai import' /app/processors/pdf.py", "Check for OpenAI imports"),
        
        # Check for Mistral imports in the container
        ("docker-compose exec celery grep -n 'from mistralai import' /app/processors/pdf.py", "Check for Mistral imports"),
        
        # Check the __init__.py file in the container
        ("docker-compose exec celery cat /app/processors/__init__.py", "Check processors __init__.py in container"),
        
        # Check environment variables in the container
        ("docker-compose exec celery env | grep -E '(MISTRAL|OPENAI)_API_KEY'", "Check API keys in container"),
        
        # Check if there are any other PDF files
        ("docker-compose exec celery find /app -name '*pdf*.py' -type f", "Find all PDF-related Python files"),
        
        # Check the Docker image build date
        ("docker-compose exec celery stat /app/processors/pdf.py", "Check PDF file timestamp in container"),
        
        # Check recent logs for API calls
        ("docker-compose logs --tail=50 celery | grep -E '(HTTP Request|OpenAI|Mistral|api\\.openai|api\\.mistral)'", "Check recent API calls in logs"),
    ]
    
    for cmd, description in commands:
        logger.info(f"\n{'='*60}")
        run_command(cmd, description)
        logger.info("")
    
    # Additional checks
    logger.info("🔍 Additional Debugging Steps:")
    logger.info("1. Check if the container was actually rebuilt:")
    logger.info("   docker-compose exec celery cat /app/processors/pdf.py | grep -A5 -B5 'client.embeddings.create'")
    logger.info("")
    logger.info("2. Force rebuild the container:")
    logger.info("   docker-compose down")
    logger.info("   docker-compose build --no-cache celery")
    logger.info("   docker-compose up -d")
    logger.info("")
    logger.info("3. Check if there are volume mounts overriding the code:")
    logger.info("   docker-compose config | grep -A10 -B10 volumes")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("Debugging interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Debugging failed: {str(e)}")
        sys.exit(1)
