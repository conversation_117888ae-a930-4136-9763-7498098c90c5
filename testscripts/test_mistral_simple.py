#!/usr/bin/env python3
"""
Simple test to check Mistral API key and basic functionality.
"""

import os
from dotenv import load_dotenv

def test_api_key():
    """Test if API key is set and valid format."""
    load_dotenv()
    api_key = os.getenv('MISTRAL_API_KEY')
    
    print("🔑 API Key Check:")
    if not api_key:
        print("❌ MISTRAL_API_KEY not found in environment")
        return False
    
    print(f"✅ API Key found: {api_key[:8]}...{api_key[-4:]}")
    print(f"   Length: {len(api_key)} characters")
    
    # Basic format check
    if len(api_key) < 20:
        print("⚠️  API key seems too short")
        return False
    
    return True

def test_mistral_import():
    """Test if Mistral client can be imported and initialized."""
    print("\n📦 Import Test:")
    
    try:
        from mistralai import Mistral
        print("✅ Mistral client imported successfully")
        
        # Try to create client
        load_dotenv()
        api_key = os.getenv('MISTRAL_API_KEY')
        client = Mistral(api_key=api_key)
        print("✅ Mistral client created successfully")
        
        return True, client
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False, None
    except Exception as e:
        print(f"❌ Client creation failed: {e}")
        return False, None

def test_simple_request(client):
    """Test a simple API request."""
    print("\n🌐 API Request Test:")
    
    try:
        response = client.chat.complete(
            model="mistral-small-2503",
            messages=[
                {"role": "user", "content": "Say 'Hello World' and nothing else."}
            ],
            max_tokens=10
        )
        
        if hasattr(response, 'choices') and response.choices:
            content = response.choices[0].message.content
            print(f"✅ API request successful!")
            print(f"   Response: {content}")
            return True
        else:
            print("❌ No response received")
            return False
            
    except Exception as e:
        error_str = str(e)
        print(f"❌ API request failed: {error_str}")
        
        # Provide specific guidance based on error
        if "401" in error_str or "Unauthorized" in error_str:
            print("💡 This suggests your API key is invalid or expired")
            print("   Please check your Mistral AI account and regenerate the key")
        elif "quota" in error_str.lower() or "limit" in error_str.lower():
            print("💡 This suggests you've hit rate limits or quota")
            print("   Please wait a moment and try again")
        elif "model_not_found" in error_str.lower():
            print("💡 The model 'mistral-small-2503' is not available")
            print("   Your account might not have access to this model")
        
        return False

def main():
    """Run all tests."""
    print("🚀 Mistral AI Simple Test")
    print("=" * 40)
    
    # Test 1: API Key
    if not test_api_key():
        print("\n❌ API key test failed. Please check your .env file.")
        return
    
    # Test 2: Import
    success, client = test_mistral_import()
    if not success:
        print("\n❌ Import test failed. Please check your installation.")
        return
    
    # Test 3: Simple request
    if test_simple_request(client):
        print("\n🎉 All tests passed! Your Mistral AI setup is working.")
        print("\n💡 Next steps:")
        print("   1. Run: uv run test_mistral_models.py")
        print("   2. Run: uv run test_mistral_knowledge_extraction.py --debug")
    else:
        print("\n❌ API request failed. Please check your API key and account status.")
        print("\n🔗 Helpful links:")
        print("   - Mistral AI Console: https://console.mistral.ai/")
        print("   - API Documentation: https://docs.mistral.ai/")

if __name__ == "__main__":
    main()
