#!/usr/bin/env python3
"""
Test script to verify PDF processing with Mistral AI.
Tests semantic chunking and embedding generation.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from processors.pdf import get_semantic_chunks, sliding_window_chunking
from mistralai import Mistral

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

def test_semantic_chunking():
    """Test semantic chunking with Mistral AI."""
    logger.info("🧪 Testing Semantic Chunking with Mistral AI")
    logger.info("=" * 50)
    
    # Sample text for testing
    test_text = """
    Docker is a containerization platform that allows developers to package applications and their dependencies into lightweight containers. 
    Docker containers run on Docker Engine, which is available for Linux, Windows, and macOS.
    
    Docker Hub is a cloud-based registry service where developers can store and share container images. 
    It provides both public and private repositories for container images.
    
    Kubernetes is an orchestration platform that manages Docker containers at scale. 
    It provides features like automatic scaling, load balancing, and service discovery.
    Kubernetes can deploy applications across multiple nodes in a cluster.
    
    Container orchestration is essential for managing complex applications in production environments.
    It helps ensure high availability and efficient resource utilization.
    """
    
    try:
        # Test semantic chunking
        logger.info("Testing semantic chunking...")
        chunks = get_semantic_chunks(test_text)
        
        logger.info(f"✅ Semantic chunking successful!")
        logger.info(f"   Generated {len(chunks)} chunks")
        
        for i, chunk in enumerate(chunks):
            logger.info(f"   Chunk {i+1}: {len(chunk)} characters")
            logger.info(f"   Preview: {chunk[:100]}...")
            logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Semantic chunking failed: {str(e)}")
        return False

def test_sliding_window_fallback():
    """Test sliding window chunking fallback."""
    logger.info("🔄 Testing Sliding Window Fallback")
    logger.info("=" * 50)
    
    test_text = "This is a simple test text for sliding window chunking. " * 20
    
    try:
        chunks = sliding_window_chunking(test_text, window_size=200, overlap=50)
        
        logger.info(f"✅ Sliding window chunking successful!")
        logger.info(f"   Generated {len(chunks)} chunks")
        
        for i, chunk in enumerate(chunks):
            logger.info(f"   Chunk {i+1}: {len(chunk)} characters")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Sliding window chunking failed: {str(e)}")
        return False

def test_mistral_api_directly():
    """Test Mistral API directly for chunking."""
    logger.info("🔗 Testing Mistral API Directly")
    logger.info("=" * 50)
    
    load_dotenv()
    api_key = os.getenv('MISTRAL_API_KEY')
    
    if not api_key:
        logger.error("❌ MISTRAL_API_KEY not found")
        return False
    
    try:
        client = Mistral(api_key=api_key)
        
        test_text = """
        Docker is a containerization platform. It allows developers to package applications.
        Kubernetes is an orchestration platform. It manages containers at scale.
        """
        
        prompt = f"""Split the following text into semantic chunks. Each chunk should be a coherent unit of information.
        Return only the chunks, one per line, with '---' as separator.

        Text to split:
        {test_text}
        """
        
        response = client.chat.complete(
            model="mistral-small-2503",
            messages=[
                {"role": "system", "content": "You are an expert at splitting text into semantic chunks. Follow the user's instructions exactly."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1
        )
        
        if response.choices and response.choices[0].message.content:
            content = response.choices[0].message.content
            logger.info(f"✅ Mistral API response received!")
            logger.info(f"   Response: {content}")
            
            chunks = [chunk.strip() for chunk in content.split('---') if chunk.strip()]
            logger.info(f"   Parsed {len(chunks)} chunks")
            
            return True
        else:
            logger.error("❌ Empty response from Mistral API")
            return False
            
    except Exception as e:
        logger.error(f"❌ Mistral API test failed: {str(e)}")
        return False

def test_embedding_generation():
    """Test embedding generation with Mistral."""
    logger.info("🔢 Testing Embedding Generation")
    logger.info("=" * 50)
    
    load_dotenv()
    api_key = os.getenv('MISTRAL_API_KEY')
    
    if not api_key:
        logger.error("❌ MISTRAL_API_KEY not found")
        return False
    
    try:
        client = Mistral(api_key=api_key)
        
        test_text = "Docker is a containerization platform for developers."
        
        response = client.embeddings.create(
            model="mistral-embed",
            inputs=[test_text]
        )
        
        if response.data and response.data[0].embedding:
            embedding = response.data[0].embedding
            logger.info(f"✅ Embedding generation successful!")
            logger.info(f"   Embedding dimensions: {len(embedding)}")
            logger.info(f"   First 5 values: {embedding[:5]}")
            return True
        else:
            logger.error("❌ No embedding data received")
            return False
            
    except Exception as e:
        logger.error(f"❌ Embedding generation failed: {str(e)}")
        return False

def main():
    """Main function to run all tests."""
    logger.info("🚀 Starting PDF Processing Tests with Mistral AI")
    
    tests = [
        ("Mistral API Direct Test", test_mistral_api_directly),
        ("Embedding Generation", test_embedding_generation),
        ("Semantic Chunking", test_semantic_chunking),
        ("Sliding Window Fallback", test_sliding_window_fallback),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*60}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*60}")
        
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {str(e)}")
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*60}")
    logger.info(f"Tests passed: {passed}/{total}")
    logger.info(f"Success rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! PDF processing with Mistral AI is working correctly.")
    elif passed >= total * 0.75:
        logger.info("✅ Most tests passed. Minor issues may need attention.")
    else:
        logger.warning("⚠️  Several tests failed. Please review the issues above.")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Test execution failed: {str(e)}")
        sys.exit(1)
