#!/usr/bin/env python3
"""
PDF Management Utility for Testing

This script helps manage PDF files in the documents directory for testing.
It can copy, list, validate, and clean up PDF files.

Usage:
    python manage_test_pdfs.py list                           # List all PDFs
    python manage_test_pdfs.py copy /path/to/file.pdf         # Copy PDF to documents
    python manage_test_pdfs.py copy /path/to/file.pdf --name test.pdf  # Copy with custom name
    python manage_test_pdfs.py validate file.pdf              # Validate a PDF
    python manage_test_pdfs.py clean                          # Remove all test PDFs
"""

import os
import sys
import shutil
import argparse
import logging
from pathlib import Path
from typing import List, Optional

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class PDFManager:
    """Utility class for managing test PDF files."""
    
    def __init__(self):
        self.documents_dir = Path("documents")
        self.documents_dir.mkdir(exist_ok=True)
    
    def list_pdfs(self) -> List[Path]:
        """List all PDF files in the documents directory."""
        pdf_files = list(self.documents_dir.glob("*.pdf"))
        
        logger.info("PDF files in documents directory:")
        if not pdf_files:
            logger.info("  (no PDF files found)")
            return []
        
        total_size = 0
        for pdf_file in sorted(pdf_files):
            size = pdf_file.stat().st_size
            total_size += size
            logger.info(f"  {pdf_file.name} ({size:,} bytes)")
        
        logger.info(f"Total: {len(pdf_files)} files, {total_size:,} bytes")
        return pdf_files
    
    def validate_pdf(self, pdf_path: str) -> bool:
        """Validate that a PDF file is readable and well-formed."""
        file_path = Path(pdf_path)
        
        # Check if file exists
        if not file_path.exists():
            logger.error(f"PDF file not found: {pdf_path}")
            return False
        
        # Check file size
        file_size = file_path.stat().st_size
        if file_size == 0:
            logger.error(f"PDF file is empty: {pdf_path}")
            return False
        
        # Check PDF header
        try:
            with open(file_path, 'rb') as f:
                header = f.read(8)
                if not header.startswith(b'%PDF-'):
                    logger.error(f"File does not appear to be a valid PDF: {pdf_path}")
                    return False
        except Exception as e:
            logger.error(f"Error reading PDF file: {e}")
            return False
        
        # Try to extract some basic info
        try:
            # Try using PyPDF2 if available
            try:
                import PyPDF2
                with open(file_path, 'rb') as f:
                    reader = PyPDF2.PdfReader(f)
                    num_pages = len(reader.pages)
                    logger.info(f"✓ PDF is valid: {pdf_path}")
                    logger.info(f"  Size: {file_size:,} bytes")
                    logger.info(f"  Pages: {num_pages}")
                    
                    # Try to extract text from first page
                    if num_pages > 0:
                        first_page_text = reader.pages[0].extract_text()
                        text_length = len(first_page_text.strip())
                        logger.info(f"  First page text length: {text_length} characters")
                        if text_length > 0:
                            preview = first_page_text.strip()[:100].replace('\n', ' ')
                            logger.info(f"  Text preview: {preview}...")
                    
                    return True
                    
            except ImportError:
                # PyPDF2 not available, just check basic structure
                logger.info(f"✓ PDF appears valid: {pdf_path}")
                logger.info(f"  Size: {file_size:,} bytes")
                logger.info("  (Install PyPDF2 for detailed validation: pip install PyPDF2)")
                return True
                
        except Exception as e:
            logger.error(f"PDF validation failed: {e}")
            return False
    
    def copy_pdf(self, source_path: str, target_name: Optional[str] = None) -> bool:
        """Copy a PDF file to the documents directory."""
        source = Path(source_path)
        
        # Validate source
        if not source.exists():
            logger.error(f"Source PDF not found: {source_path}")
            return False
        
        if not self.validate_pdf(source_path):
            return False
        
        # Determine target path
        if target_name:
            if not target_name.endswith('.pdf'):
                target_name += '.pdf'
            target = self.documents_dir / target_name
        else:
            target = self.documents_dir / source.name
        
        # Check if target already exists
        if target.exists():
            logger.warning(f"Target file already exists: {target}")
            response = input("Overwrite? (y/N): ").strip().lower()
            if response != 'y':
                logger.info("Copy cancelled")
                return False
        
        try:
            shutil.copy2(source, target)
            logger.info(f"✓ PDF copied successfully")
            logger.info(f"  From: {source}")
            logger.info(f"  To: {target}")
            
            # Verify the copy
            if target.exists() and target.stat().st_size == source.stat().st_size:
                logger.info("✓ Copy verified")
                return True
            else:
                logger.error("✗ Copy verification failed")
                return False
                
        except Exception as e:
            logger.error(f"Copy failed: {e}")
            return False
    
    def clean_pdfs(self, confirm: bool = True) -> bool:
        """Remove all PDF files from the documents directory."""
        pdf_files = list(self.documents_dir.glob("*.pdf"))
        
        if not pdf_files:
            logger.info("No PDF files to clean")
            return True
        
        logger.info(f"Found {len(pdf_files)} PDF files to remove:")
        for pdf_file in pdf_files:
            logger.info(f"  {pdf_file.name}")
        
        if confirm:
            response = input(f"Remove all {len(pdf_files)} PDF files? (y/N): ").strip().lower()
            if response != 'y':
                logger.info("Clean cancelled")
                return False
        
        removed_count = 0
        for pdf_file in pdf_files:
            try:
                pdf_file.unlink()
                logger.info(f"✓ Removed: {pdf_file.name}")
                removed_count += 1
            except Exception as e:
                logger.error(f"✗ Failed to remove {pdf_file.name}: {e}")
        
        logger.info(f"Removed {removed_count}/{len(pdf_files)} PDF files")
        return removed_count == len(pdf_files)
    
    def get_info(self, pdf_name: str) -> bool:
        """Get detailed information about a specific PDF."""
        pdf_path = self.documents_dir / pdf_name
        
        if not pdf_path.exists():
            logger.error(f"PDF not found: {pdf_name}")
            return False
        
        return self.validate_pdf(str(pdf_path))


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Manage PDF files for testing",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Commands:
  list                    List all PDF files in documents directory
  copy SOURCE [--name N]  Copy PDF file to documents directory
  validate FILE           Validate a PDF file
  clean [--force]         Remove all PDF files from documents directory
  info FILE               Get detailed info about a PDF file

Examples:
  python manage_test_pdfs.py list
  python manage_test_pdfs.py copy /path/to/document.pdf
  python manage_test_pdfs.py copy document.pdf --name test_doc.pdf
  python manage_test_pdfs.py validate sample_test_document.pdf
  python manage_test_pdfs.py clean
  python manage_test_pdfs.py info sample_test_document.pdf
        """
    )
    
    parser.add_argument("command", choices=['list', 'copy', 'validate', 'clean', 'info'],
                       help="Command to execute")
    parser.add_argument("file", nargs='?', help="PDF file path (for copy, validate, info commands)")
    parser.add_argument("--name", help="Custom name for copied file")
    parser.add_argument("--force", action="store_true", help="Skip confirmation prompts")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create PDF manager
    manager = PDFManager()
    
    try:
        if args.command == 'list':
            manager.list_pdfs()
            
        elif args.command == 'copy':
            if not args.file:
                logger.error("Source file path required for copy command")
                sys.exit(1)
            success = manager.copy_pdf(args.file, args.name)
            sys.exit(0 if success else 1)
            
        elif args.command == 'validate':
            if not args.file:
                logger.error("File path required for validate command")
                sys.exit(1)
            success = manager.validate_pdf(args.file)
            sys.exit(0 if success else 1)
            
        elif args.command == 'clean':
            success = manager.clean_pdfs(confirm=not args.force)
            sys.exit(0 if success else 1)
            
        elif args.command == 'info':
            if not args.file:
                logger.error("File name required for info command")
                sys.exit(1)
            success = manager.get_info(args.file)
            sys.exit(0 if success else 1)
            
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Operation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
