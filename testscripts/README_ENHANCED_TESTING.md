# Enhanced PDF Testing Scripts

This directory contains enhanced testing scripts that allow you to test the PDF processing API with any PDF file, not just the default sample document.

## New Scripts

### 1. `test_with_custom_pdf.py` - Test with Any PDF File

Test the PDF processing API with any PDF file you specify.

**Usage:**
```bash
# Test with a PDF file directly (no copying)
python test_with_custom_pdf.py --pdf /path/to/your/document.pdf

# Copy PDF to documents directory first, then test
python test_with_custom_pdf.py --pdf /path/to/your/document.pdf --copy

# Copy with a custom name
python test_with_custom_pdf.py --pdf /path/to/your/document.pdf --copy --name my_test.pdf

# Use different API server
python test_with_custom_pdf.py --pdf document.pdf --url http://localhost:8001

# List files in documents directory
python test_with_custom_pdf.py --list-docs
```

**Features:**
- Validates PDF files before processing
- Can copy files to the documents directory
- Supports custom naming for copied files
- Shows file sizes and processing progress
- Full error handling and validation

### 2. `run_api_tests_enhanced.py` - Enhanced Test Runner

Extended version of the original `run_api_tests.py` with custom PDF support.

**Usage:**
```bash
# Run tests with default sample PDF (same as original)
python run_api_tests_enhanced.py

# Run tests with custom PDF (direct path)
python run_api_tests_enhanced.py --pdf /path/to/document.pdf

# Copy custom PDF to documents directory first
python run_api_tests_enhanced.py --pdf /path/to/document.pdf --copy

# Copy with custom name
python run_api_tests_enhanced.py --pdf document.pdf --copy --name test_doc.pdf

# Run only Python tests with custom PDF
python run_api_tests_enhanced.py --pdf document.pdf --python-only

# List files in documents directory
python run_api_tests_enhanced.py --list-docs
```

**All original options are still supported:**
- `--quick-only`, `--python-only`, `--curl-only`
- `--skip-server-check`, `--skip-python`, `--skip-curl`
- `--url`, `--verbose`

### 3. `manage_test_pdfs.py` - PDF Management Utility

Utility script to manage PDF files in the documents directory.

**Usage:**
```bash
# List all PDF files in documents directory
python manage_test_pdfs.py list

# Copy a PDF file to documents directory
python manage_test_pdfs.py copy /path/to/document.pdf

# Copy with custom name
python manage_test_pdfs.py copy /path/to/document.pdf --name test_doc.pdf

# Validate a PDF file
python manage_test_pdfs.py validate sample_test_document.pdf

# Get detailed info about a PDF
python manage_test_pdfs.py info sample_test_document.pdf

# Clean up all PDF files (with confirmation)
python manage_test_pdfs.py clean

# Clean up without confirmation
python manage_test_pdfs.py clean --force
```

## Quick Start Examples

### Test with Your Own PDF

1. **Direct testing** (PDF stays in original location):
   ```bash
   python test_with_custom_pdf.py --pdf ~/Downloads/my_document.pdf
   ```

2. **Copy to documents directory first**:
   ```bash
   python test_with_custom_pdf.py --pdf ~/Downloads/my_document.pdf --copy --name my_test.pdf
   ```

3. **Run full test suite with custom PDF**:
   ```bash
   python run_api_tests_enhanced.py --pdf ~/Downloads/my_document.pdf --copy
   ```

### Manage Test Files

1. **See what PDF files you have**:
   ```bash
   python manage_test_pdfs.py list
   ```

2. **Copy a new PDF for testing**:
   ```bash
   python manage_test_pdfs.py copy ~/Documents/important_doc.pdf --name test_important.pdf
   ```

3. **Validate a PDF before testing**:
   ```bash
   python manage_test_pdfs.py validate test_important.pdf
   ```

4. **Clean up when done**:
   ```bash
   python manage_test_pdfs.py clean
   ```

## File Validation

All scripts include comprehensive PDF validation:

- ✅ File exists and is readable
- ✅ File has valid PDF header (`%PDF-`)
- ✅ File size is reasonable (warns if > 50MB)
- ✅ Basic PDF structure validation
- ✅ Page count and text extraction (if PyPDF2 is installed)

## Error Handling

The enhanced scripts provide detailed error messages for common issues:

- **File not found**: Clear path information
- **Invalid PDF**: Specific validation failure details
- **Copy failures**: Permission and disk space issues
- **API errors**: Server connectivity and response issues
- **Processing failures**: Task status and error details

## Integration with Existing Scripts

The enhanced scripts are designed to work alongside the existing test scripts:

- `test_api_python.py` - Original Python API tester (still works)
- `test_api_curl.sh` - Original curl-based tests (still works)
- `run_api_tests.py` - Original test runner (still works)

You can use either the original or enhanced versions based on your needs.

## Dependencies

**Required:**
- Python 3.7+
- `requests` library
- Standard library modules

**Optional (for enhanced PDF validation):**
- `PyPDF2` - For detailed PDF analysis
- `reportlab` - For creating sample PDFs

Install optional dependencies:
```bash
pip install PyPDF2 reportlab
```

## Tips

1. **Large PDFs**: Files over 50MB may take longer to process. The scripts will warn you about large files.

2. **PDF Quality**: The API works best with text-based PDFs. Scanned images may not extract text well.

3. **File Organization**: Use the `--copy` option to keep your original files safe and organize test files in the documents directory.

4. **Debugging**: Use `--verbose` flag for detailed logging and debugging information.

5. **Cleanup**: Regularly use `manage_test_pdfs.py clean` to remove old test files.

## Troubleshooting

**"PDF file not found"**: Check the file path and ensure the file exists.

**"File does not appear to be a valid PDF"**: The file may be corrupted or not a real PDF.

**"Copy failed"**: Check file permissions and available disk space.

**"API server not responding"**: Ensure the server is running on the correct port.

**"Processing timeout"**: Large files may need more time. Check server logs for details.
