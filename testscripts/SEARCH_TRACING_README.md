# Search Tracing Documentation

This document explains how to use the search tracing functionality to understand how the search engine works, with special focus on the Chinese query "列出所有庫易股票資料" as requested.

## Overview

The search tracing functionality provides detailed insights into:
- Vector similarity search performance
- Text search effectiveness  
- Result merging and scoring
- Chinese query handling
- Knowledge graph integration
- AI response generation

## Files Added/Modified

### New Files
- `test_search_traces.py` - Standalone search tracing script
- `run_search_trace_demo.py` - Interactive demo script
- `SEARCH_TRACING_README.md` - This documentation

### Modified Files
- `test_web_interface.py` - Enhanced with detailed search tracing

## Quick Start

### 1. Basic Search Tracing
Test the Chinese query with detailed tracing:
```bash
python test_search_traces.py --query "列出所有庫易股票資料"
```

### 2. Interactive Demo
Run the interactive demo to explore different options:
```bash
python run_search_trace_demo.py
```

### 3. Web Interface with Search Traces
Test only search functionality from web interface:
```bash
python test_web_interface.py --search-traces-only
```

## Detailed Usage

### test_search_traces.py Options

```bash
# Test specific query
python test_search_traces.py --query "your query here"

# Save debug output to files
python test_search_traces.py --query "列出所有庫易股票資料" --save-debug

# Get more results for analysis
python test_search_traces.py --query "列出所有庫易股票資料" --top-k 10

# Use different API URL
python test_search_traces.py --api-url http://localhost:8000

# Verbose output
python test_search_traces.py --query "列出所有庫易股票資料" --verbose
```

### test_web_interface.py Options

```bash
# Run only search traces
python test_web_interface.py --search-traces-only

# Run search traces with custom query
python test_web_interface.py --search-traces-only --custom-query "列出所有庫易股票資料"

# Skip search traces in full test
python test_web_interface.py --skip-search-traces

# Full test with different URLs
python test_web_interface.py --web-url http://localhost:5000 --api-url http://localhost:8000
```

## What the Tracing Shows

### 1. Request Setup
- Query preprocessing
- API request configuration
- Debug mode enablement

### 2. API Performance
- Request/response timing
- Status code verification
- Error handling

### 3. Search Results Analysis
- **Score Distribution**: Vector, text, and combined scores
- **Content Analysis**: Length, entities, relationships
- **Top Results Breakdown**: Detailed analysis of best matches

### 4. Chinese Query Special Analysis
- Chinese content detection
- Stock code identification (4-digit numbers)
- Language-specific search effectiveness

### 5. Knowledge Graph Integration
- Entity extraction from results
- Relationship mapping
- Knowledge connectivity analysis

## Example Output

When running with the Chinese query "列出所有庫易股票資料", you'll see:

```
🔍 DETAILED SEARCH TRACE: '列出所有庫易股票資料'
================================================================================

📋 STAGE 1: Request Setup
   • Query: '列出所有庫易股票資料'
   • Top K: 5
   • Debug mode: Enabled
   • Setup time: 0.001s

🚀 STAGE 2: Making API Request
   • API call duration: 1.234s
   • Response status: 200

📊 STAGE 3: Response Analysis
   • Parsing time: 0.002s
   • Results found: 5

🔬 STAGE 4: Detailed Results Analysis
   📈 PERFORMANCE METRICS:
      • Total execution time: 1.180s
      • Results returned: 5
      • AI response generated: Yes
   
   📊 SCORE ANALYSIS:
      • Vector scores: min=0.1234, max=0.8765, avg=0.4567
      • Text scores: min=0.2345, max=0.9876, avg=0.5678
      • Combined scores: min=0.1789, max=0.9321, avg=0.5123
   
   🇨🇳 CHINESE QUERY ANALYSIS:
      • Results with Chinese content: 4/5 (80.0%)
      • Results with stock codes: 3/5 (60.0%)
```

## Debug Output Files

When using `--save-debug`, the following files are created in `search_debug_output/`:

- `search_trace_[timestamp].json` - Complete trace data
- `search_results_[timestamp].json` - Full search results

## Understanding the Results

### Score Interpretation
- **Vector Score**: Semantic similarity (0.0-1.0)
- **Text Score**: Keyword matching effectiveness (0.0-1.0)  
- **Combined Score**: Weighted combination of both

### Chinese Query Analysis
- **Chinese Content %**: How many results contain Chinese text
- **Stock Code Detection**: Results containing 4-digit stock codes
- **Language Matching**: Effectiveness for Chinese queries

### Performance Metrics
- **API Call Time**: Network and processing time
- **Search Execution**: Internal search engine time
- **Result Processing**: Analysis and formatting time

## Troubleshooting

### Common Issues

1. **No Results Found**
   - Check if documents are loaded in the database
   - Verify API connectivity
   - Try simpler queries first

2. **API Connection Failed**
   - Ensure backend services are running
   - Check API URL and port
   - Verify API key is set

3. **Chinese Query Issues**
   - Ensure database contains Chinese content
   - Check text encoding settings
   - Verify Mistral AI supports Chinese

### Debug Steps

1. **Test API Connectivity**
   ```bash
   python test_web_interface.py --api-url http://localhost:8000
   ```

2. **Check Database Content**
   ```bash
   python test_search_traces.py --query "test" --verbose
   ```

3. **Save Debug Output**
   ```bash
   python test_search_traces.py --query "列出所有庫易股票資料" --save-debug
   ```

## Integration with Existing Tests

The search tracing integrates seamlessly with existing test infrastructure:

- **Standalone**: Use `test_search_traces.py` for focused search analysis
- **Web Interface**: Enhanced `test_web_interface.py` includes search traces
- **Demo Mode**: Use `run_search_trace_demo.py` for interactive exploration

## Next Steps

1. **Run the Chinese Query Test**:
   ```bash
   python test_search_traces.py --query "列出所有庫易股票資料" --save-debug
   ```

2. **Analyze Results**: Review the detailed output and debug files

3. **Optimize Search**: Use insights to improve search configuration

4. **Test Other Queries**: Expand testing with additional Chinese queries

## Support

For issues or questions about search tracing:
1. Check the debug output files
2. Review the detailed logs
3. Test with simpler queries first
4. Verify backend services are running
