# KAG Web Interface

A user-friendly Flask web interface for the KAG (Knowledge Augmented Generation) document processing system. This web application provides an intuitive way to interact with your document processing API without needing to use command-line tools or make direct API calls.

## Features

### 📊 Dashboard
- Overview of knowledge base statistics
- Quick access to all major functions
- Recent document activity
- System health monitoring

### 📤 Document Upload
- Drag-and-drop file upload
- Support for PDF and video files
- Real-time upload progress
- File validation and preview

### 🔍 Search Interface
- Natural language search queries
- Configurable result count
- Debug mode for detailed search information
- Search result highlighting
- AI-generated responses

### 📁 Document Management
- View all uploaded documents
- Filter and search documents
- Processing status monitoring
- Document detail views with chunks
- Cancel processing for active documents

### 🕸️ Knowledge Graph Visualization
- Interactive 3D knowledge graph
- Entity and relationship visualization
- Category-based color coding
- Zoom, pan, and navigation controls
- Node details and statistics

### ⚡ Real-time Status Updates
- Live processing status monitoring
- Auto-refresh for active processes
- Detailed processing pipeline view
- Error reporting and troubleshooting

## Installation

1. **Install Flask** (if not already installed):
   ```bash
   pip install flask
   ```

2. **Ensure your API is running**:
   Make sure your FastAPI backend is running on `http://localhost:8000` (or update the `API_BASE_URL` in your `.env` file).

3. **Configure environment variables** in your `.env` file:
   ```env
   # API Configuration
   API_BASE_URL=http://localhost:8000
   API_KEY=your-api-key-here
   
   # Flask Configuration
   FLASK_HOST=0.0.0.0
   FLASK_PORT=5000
   FLASK_DEBUG=False
   FLASK_SECRET_KEY=your-secret-key-here
   ```

## Usage

### Starting the Web Interface

**Option 1: Using the startup script**
```bash
python start_web.py
```

**Option 2: Direct Flask execution**
```bash
python web_app.py
```

**Option 3: Using uv (if available)**
```bash
uv run start_web.py
```

### Accessing the Interface

Once started, open your web browser and navigate to:
```
http://localhost:5000
```

## Web Interface Structure

```
/                    - Dashboard with overview and statistics
/upload             - Document upload page
/search             - Search interface
/documents          - Document management
/document/<id>      - Individual document details
/status/<id>        - Processing status for a document
/graph              - Knowledge graph visualization
```

## API Integration

The web interface communicates with your FastAPI backend through the following endpoints:

- `GET /health` - System health check
- `POST /upload-pdf` - PDF document upload
- `POST /upload-video` - Video file upload
- `POST /kag-search` - Document search
- `GET /kbdata` - Knowledge base statistics
- `GET /graph-data` - Knowledge graph data
- `GET /processing-status/<id>` - Document processing status
- `GET /doc-chunks` - Document chunks
- `DELETE /cancel-processing/<id>` - Cancel processing

## Features in Detail

### Document Upload
- **Supported formats**: PDF, MP4, AVI, MOV, MKV, WMV
- **File size limit**: 50MB (configurable)
- **Validation**: Client-side file type and size validation
- **Progress tracking**: Real-time upload progress with visual feedback

### Search Functionality
- **Natural language queries**: Ask questions in plain English
- **Configurable results**: Choose number of results (3-20)
- **Debug mode**: See detailed search process information
- **Result highlighting**: Search terms highlighted in results
- **AI responses**: Generated responses based on search results

### Document Management
- **Status filtering**: Filter by processing status
- **Real-time updates**: Auto-refresh for processing documents
- **Bulk operations**: Cancel multiple processing tasks
- **Detailed views**: View document chunks and metadata

### Knowledge Graph
- **Interactive visualization**: 3D force-directed graph
- **Entity categories**: Color-coded by entity type
- **Relationship mapping**: Visual representation of connections
- **Navigation controls**: Zoom, pan, reset view
- **Node details**: Click nodes for detailed information

## Customization

### Styling
The interface uses Bootstrap 5 for responsive design. You can customize the appearance by:

1. **Modifying CSS**: Edit the `<style>` sections in template files
2. **Adding custom CSS**: Create a `static/css/custom.css` file
3. **Changing themes**: Update Bootstrap classes in templates

### Configuration
Key configuration options in `web_app.py`:

```python
# File upload limits
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB

# Allowed file extensions
ALLOWED_EXTENSIONS = {'pdf', 'mp4', 'avi', 'mov', 'mkv', 'wmv'}

# API configuration
API_BASE_URL = os.getenv('API_BASE_URL', 'http://localhost:8000')
API_KEY = os.getenv('API_KEY')
```

## Troubleshooting

### Common Issues

1. **"Connection refused" errors**:
   - Ensure your FastAPI backend is running
   - Check the `API_BASE_URL` in your `.env` file
   - Verify the API is accessible at the specified URL

2. **"API key invalid" errors**:
   - Check that `API_KEY` is set correctly in `.env`
   - Ensure the API key matches your backend configuration

3. **Upload failures**:
   - Check file size limits (default 50MB)
   - Verify file format is supported
   - Ensure sufficient disk space

4. **Graph not loading**:
   - Check browser console for JavaScript errors
   - Ensure the `/graph-data` API endpoint is working
   - Verify you have documents with entities and relationships

### Debug Mode

Enable debug mode for detailed error information:
```env
FLASK_DEBUG=True
```

**Note**: Only use debug mode in development, never in production.

## Security Considerations

- **API Key**: Keep your API key secure and never commit it to version control
- **HTTPS**: Use HTTPS in production environments
- **File uploads**: Validate and sanitize all uploaded files
- **CORS**: Configure CORS settings appropriately for your deployment

## Production Deployment

For production deployment, consider:

1. **Use a production WSGI server** (e.g., Gunicorn, uWSGI)
2. **Set up reverse proxy** (e.g., Nginx, Apache)
3. **Enable HTTPS** with SSL certificates
4. **Configure logging** and monitoring
5. **Set secure session keys** and other security headers

Example Gunicorn command:
```bash
gunicorn -w 4 -b 0.0.0.0:5000 web_app:app
```

## Support

If you encounter issues:

1. Check the console output for error messages
2. Verify your API backend is running and accessible
3. Check the browser developer tools for client-side errors
4. Review the configuration in your `.env` file

The web interface provides a comprehensive, user-friendly way to interact with your KAG document processing system, making it accessible to users who prefer graphical interfaces over command-line tools or direct API calls.
