-- <PERSON><PERSON><PERSON> to create Document_Embeddings table with <PERSON><PERSON><PERSON>'s 1024-dimension vectors

-- Drop the table if it exists
DROP TABLE IF EXISTS Document_Embeddings;

-- Create table with Mistral dimensions (1024)
CREATE TABLE Document_Embeddings (
  embedding_id BIGINT PRIMARY KEY AUTO_INCREMENT,
  doc_id BIGINT NOT NULL,
  content TEXT,
  embedding VECTOR(1024),  -- Using Mistral's 1024-dimension vectors instead of OpenAI's 1536
  chunk_metadata_id BIGINT,
  SORT KEY(),
  FULLTEXT USING VERSION 2 content_ft_idx (content)
);

-- Ensure table is indexed
OPTIMIZE TABLE Document_Embeddings FLUSH;

-- Create Documents table if it doesn't exist
CREATE TABLE IF NOT EXISTS Documents (
  doc_id BIGINT PRIMARY KEY AUTO_INCREMENT,
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(512) NOT NULL,
  file_size BIGINT NOT NULL,
  current_step ENUM('started', 'chunking', 'embeddings', 'entities', 'relationships', 'completed', 'failed') NOT NULL,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create Chunk_Metadata table if it doesn't exist
CREATE TABLE IF NOT EXISTS Chunk_Metadata (
  chunk_id BIGINT PRIMARY KEY AUTO_INCREMENT,
  doc_id BIGINT NOT NULL,
  chunk_index INT NOT NULL,
  start_char INT,
  end_char INT,
  metadata JSON
);

-- Create Entities table if it doesn't exist
CREATE TABLE IF NOT EXISTS Entities (
  entity_id BIGINT NOT NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  aliases JSON,
  category VARCHAR(100),
  PRIMARY KEY (entity_id, name),
  SHARD KEY (entity_id, name)
);

-- Create Relationships table if it doesn't exist
CREATE TABLE IF NOT EXISTS Relationships (
  relationship_id BIGINT PRIMARY KEY AUTO_INCREMENT,
  source_entity_id BIGINT NOT NULL,
  target_entity_id BIGINT NOT NULL,
  relation_type VARCHAR(100) NOT NULL,
  doc_id BIGINT,
  metadata JSON
);
