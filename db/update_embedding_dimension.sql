-- SQL Script to update the embedding dimension from 1536 (OpenAI) to 1024 (Mistral)

-- First drop the existing vector index
-- DROP INDEX embedding_vec_idx ON Document_Embeddings;

-- Modify the embedding column to use 1024 dimensions
ALTER TABLE Document_Embeddings 
MODIFY COLUMN embedding VECTOR(1024);

-- Recreate the vector index with the new dimension
ALTER TABLE Document_Embeddings 
ADD VECTOR INDEX embedding_vec_idx (embedding)
INDEX_OPTIONS '{"index_type": "HNSW_FLAT", "metric_type": "DOT_PRODUCT", "M": 32, "efConstruction": 200}';

-- Verify the change
DESCRIBE Document_Embeddings;
