-- <PERSON><PERSON> to clear all data from the database
-- This script deletes all data while respecting foreign key constraints

-- Disable foreign key checks temporarily to avoid constraint issues
SET FOREIGN_KEY_CHECKS = 0;

-- Clear Document_Embeddings table
TRUNCATE TABLE Document_Embeddings;

-- Clear Relationships table
TRUNCATE TABLE Relationships;

-- Clear Chunk_Metadata table
TRUNCATE TABLE Chunk_Metadata;

-- Clear ProcessingStatus table
TRUNCATE TABLE ProcessingStatus;

-- Clear Entities table
TRUNCATE TABLE Entities;

-- Clear Documents table
TRUNCATE TABLE Documents;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Verify tables are empty
SELECT 'Documents' as table_name, COUNT(*) as row_count FROM Documents
UNION ALL
SELECT 'Document_Embeddings', COUNT(*) FROM Document_Embeddings
UNION ALL
SELECT 'Entities', COUNT(*) FROM Entities
UNION ALL
SELECT 'Relationships', COUNT(*) FROM Relationships
UNION ALL
SELECT 'Chunk_Metadata', COUNT(*) FROM Chunk_Metadata
UNION ALL
SELECT 'ProcessingStatus', COUNT(*) FROM ProcessingStatus;

-- Optional: Reset auto-increment values
ALTER TABLE Documents AUTO_INCREMENT = 1;
ALTER TABLE Document_Embeddings AUTO_INCREMENT = 1;
ALTER TABLE Entities AUTO_INCREMENT = 1;
ALTER TABLE Relationships AUTO_INCREMENT = 1;
ALTER TABLE Chunk_Metadata AUTO_INCREMENT = 1;
