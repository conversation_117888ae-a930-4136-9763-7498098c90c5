-- Drop tables in reverse order of dependencies
DROP TABLE IF EXISTS ProcessingStatus;
DROP TABLE IF EXISTS Document_Embeddings;
DROP TABLE IF EXISTS Relationships;
DROP TABLE IF EXISTS Chunk_Metadata;
DROP TABLE IF EXISTS Entities;
DROP TABLE IF EXISTS Documents;
-- Knowledge Graph Database Schema
-- Tables for document storage and vector search

-- Documents table for storing document metadata
CREATE TABLE Documents (
  doc_id BIGINT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(255),
  author VARCHAR(100),
  publish_date DATE,
  source VARCHAR(255)
  -- Other metadata fields (e.g. summary, URL) can be added as needed
);

CREATE TABLE Entities (
  entity_id BIGINT NOT NULL AUTO_INCREMENT,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  aliases JSON,
  category VARCHAR(100),
  PRIMARY <PERSON>EY (entity_id, name),
  SHARD KEY (entity_id, name),
  FULLTEXT USING VERSION 2 name_ft_idx (name)
);



-- Relationships table for knowledge graph edges
CREATE TABLE Relationships (
  relationship_id BIGINT PRIMARY KEY AUTO_INCREMENT,
  source_entity_id BIGINT NOT NULL,
  target_entity_id BIGINT NOT NULL,
  relation_type VARCHAR(100),
  doc_id BIGINT,   -- reference to Documents.doc_id (not an enforced foreign key)
  KEY (source_entity_id) USING HASH,  -- index for quickly finding relationships by source
  KEY (target_entity_id) USING HASH,  -- index for quickly finding relationships by target
  KEY (doc_id)                        -- index for querying relationships by document
);


-- Chunk metadata for document segmentation
CREATE TABLE Chunk_Metadata (
    chunk_id BIGINT NOT NULL AUTO_INCREMENT,
    doc_id BIGINT NOT NULL,
    position INT NOT NULL,
    section_path TEXT,
    prev_chunk_id BIGINT,
    next_chunk_id BIGINT,
    overlap_start_id BIGINT,
    overlap_end_id BIGINT,
    semantic_unit VARCHAR(255),
    structural_context JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (doc_id, chunk_id), 
    SHARD KEY (doc_id),
    SORT KEY(),  -- Columnstore table
    KEY (prev_chunk_id) USING HASH,
    KEY (next_chunk_id) USING HASH,
    KEY (overlap_start_id) USING HASH,
    KEY (overlap_end_id) USING HASH
);

-- Add fulltext index on section_path

-- Document embeddings for vector search
CREATE TABLE Document_Embeddings (
  embedding_id BIGINT PRIMARY KEY AUTO_INCREMENT,
  doc_id       BIGINT NOT NULL,
  content      TEXT,
  embedding    VECTOR(1024),
  chunk_metadata_id BIGINT,
  SORT KEY(),  -- Ensure this is a columnstore table&#8203;:contentReference[oaicite:11]{index=11}
  FULLTEXT USING VERSION 2 content_ft_idx (content),  -- Full-Text index (v2) on content&#8203;:contentReference[oaicite:12]{index=12}
  VECTOR INDEX embedding_vec_idx (embedding)
    INDEX_OPTIONS '{ "index_type": "HNSW_FLAT", "metric_type": "DOT_PRODUCT" }'   -- Vector index on embedding column&#8203;:contentReference[oaicite:13]{index=13}    
);

-- Add vector index for semantic search
-- ALTER TABLE Document_Embeddings 
-- ADD VECTOR INDEX embedding_vec_idx (embedding)
-- INDEX_OPTIONS '{"index_type": "HNSW_FLAT", "metric_type": "DOT_PRODUCT", "M": 32, "efConstruction": 200}';

-- Entities table for knowledge graph nodes

-- Processing status tracking table
CREATE TABLE ProcessingStatus (
  doc_id BIGINT PRIMARY KEY,
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(512) NOT NULL,
  file_size BIGINT NOT NULL,
  current_step ENUM('started', 'chunking', 'embeddings', 'entities', 'relationships', 'completed', 'failed') NOT NULL,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Ensure recent data is indexed
OPTIMIZE TABLE Document_Embeddings FLUSH;

-- Example query for hybrid search
/*
SELECT
  doc_id,
  content,
  MATCH (TABLE Document_Embeddings) AGAINST ('How does SingleStore support hybrid search in RAG?') AS score
FROM Document_Embeddings
WHERE MATCH (TABLE Document_Embeddings) AGAINST ('How does SingleStore support hybrid search in RAG?')
ORDER BY score DESC
LIMIT 10;
*/
