#!/usr/bin/env python3
"""
Startup script for the Flask web interface.
This script starts the Flask web application that provides a user-friendly interface
to interact with the KAG document processing API.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def main():
    """Main function to start the Flask web application."""
    print("🚀 Starting KAG Web Interface...")
    print("=" * 50)
    
    # Check if required environment variables are set
    api_key = os.getenv('API_KEY')
    if not api_key:
        print("❌ Error: API_KEY environment variable is not set!")
        print("Please set the API_KEY in your .env file.")
        sys.exit(1)
    
    # Set Flask configuration
    os.environ['FLASK_HOST'] = os.getenv('FLASK_HOST', '0.0.0.0')
    os.environ['FLASK_PORT'] = os.getenv('FLASK_PORT', '5000')
    os.environ['FLASK_DEBUG'] = os.getenv('FLASK_DEBUG', 'False')
    os.environ['API_BASE_URL'] = os.getenv('API_BASE_URL', 'http://localhost:8000')
    
    print(f"📡 API Base URL: {os.getenv('API_BASE_URL')}")
    print(f"🌐 Web Interface: http://{os.getenv('FLASK_HOST')}:{os.getenv('FLASK_PORT')}")
    print(f"🔧 Debug Mode: {os.getenv('FLASK_DEBUG')}")
    print()
    
    # Import and run the Flask app
    try:
        from web_app import app
        
        # Create necessary directories
        os.makedirs('templates', exist_ok=True)
        os.makedirs('static', exist_ok=True)
        
        print("✅ Flask web application starting...")
        print("💡 Access the web interface in your browser at the URL shown above")
        print("🛑 Press Ctrl+C to stop the server")
        print()
        
        # Run the Flask app
        app.run(
            host=os.getenv('FLASK_HOST', '0.0.0.0'),
            port=int(os.getenv('FLASK_PORT', 5000)),
            debug=os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
        )
        
    except ImportError as e:
        print(f"❌ Error importing Flask app: {e}")
        print("Make sure Flask is installed: pip install flask")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting Flask app: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
